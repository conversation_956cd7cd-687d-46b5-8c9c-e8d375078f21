**For Sending batch emails**

```js
import nodemailer from "nodemailer";

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_APP_PASSWORD,
  },
});

async function sendBatchEmails(recipients, subject, htmlContent, batchSize = 50, delayMs = 2000) {
  for (let i = 0; i < recipients.length; i += batchSize) {
    const batch = recipients.slice(i, i + batchSize);

    console.log(`Sending batch ${i / batchSize + 1} to ${batch.length} recipients`);

    await Promise.all(
      batch.map((email) =>
        transporter.sendMail({
          from: `"Your Blog" <<EMAIL>>`,
          to: email,
          subject,
          html: htmlContent,
          headers: {
            "List-Unsubscribe": "<mailto:<EMAIL>>, <https://yourdomain.com/unsubscribe>",
          },
        })
      )
    );

    if (i + batchSize < recipients.length) {
      console.log(`Waiting ${delayMs}ms before next batch...`);
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }
}

// Example usage:
const subscribers = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  // ... thousands more
];

sendBatchEmails(subscribers, "Weekly Blog Update", "<h1>Hello!</h1><p>Check out our latest posts...</p>");
```

**Reference Links**
[Full Code](https://www.youtube.com/watch?v=IpjNjAJrs0s)
