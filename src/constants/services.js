import { Briefcase, Globe, Rocket, Users } from "lucide-react";

export const SERVICES = [
  {
    icon: Briefcase,
    title: 'Content Strategy & Creation',
    description: 'Comprehensive content strategies, blog writing, and editorial services tailored to your brand.',
    features: ['Content Planning', 'SEO Optimization', 'Editorial Calendar', 'Brand Voice Development'],
    price: 'Starting at $2,500/month',
    color: 'text-blue-500'
  },
  {
    icon: Rocket,
    title: 'Digital Marketing & Growth',
    description: 'Full-service digital marketing to amplify your reach and drive meaningful engagement.',
    features: ['Social Media Management', 'Email Marketing', 'Influencer Partnerships', 'Analytics & Reporting'],
    price: 'Starting at $3,500/month',
    color: 'text-purple-500'
  },
  {
    icon: Globe,
    title: 'Website & Platform Development',
    description: 'Modern, responsive websites and content platforms built for performance and user experience.',
    features: ['Custom Development', 'CMS Integration', 'Performance Optimization', 'Ongoing Maintenance'],
    price: 'Starting at $5,000/project',
    color: 'text-green-500'
  },
  {
    icon: Users,
    title: 'Community Building & Management',
    description: 'Build and nurture engaged communities around your brand with our proven strategies.',
    features: ['Community Strategy', 'Engagement Programs', 'Moderation Services', 'Growth Analytics'],
    price: 'Starting at $2,000/month',
    color: 'text-orange-500'
  }
];
