// Test file to verify PocketBase authentication setup
// This file can be deleted after testing

import PocketBase from 'pocketbase';

const pb = new PocketBase('http://localhost:8090');

async function testAuthFlow() {
  try {
    console.log('Testing PocketBase connection...');
    
    // Test basic connection
    const health = await pb.health.check();
    console.log('PocketBase health check:', health);
    
    // Test if users collection exists
    try {
      const collections = await pb.collections.getList();
      const usersCollection = collections.items.find(c => c.name === 'users');
      console.log('Users collection found:', !!usersCollection);
      
      if (usersCollection) {
        console.log('Users collection config:', {
          name: usersCollection.name,
          type: usersCollection.type,
          authRule: usersCollection.authRule,
          passwordAuth: usersCollection.passwordAuth,
          otp: usersCollection.otp
        });
      }
    } catch (err) {
      console.log('Could not fetch collections (might need admin auth):', err.message);
    }
    
    // Test OTP functionality (this will fail without valid credentials, but shows if the method exists)
    try {
      await pb.collection('users').requestOTP('<EMAIL>');
    } catch (err) {
      if (err.message.includes('Failed to authenticate')) {
        console.log('✓ OTP method exists (authentication failed as expected)');
      } else {
        console.log('OTP method error:', err.message);
      }
    }
    
  } catch (error) {
    console.error('PocketBase connection failed:', error.message);
  }
}

// Uncomment the line below to run the test
// testAuthFlow();

export default testAuthFlow;
