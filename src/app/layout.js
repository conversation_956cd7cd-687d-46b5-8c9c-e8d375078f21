import { Plus_Jakarta_Sans, Red_Hat_Display } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { CompanyName } from "@/constants/companyName";
import { BaseUrl } from "@/constants/url";
import { Toaster } from "sonner";

const red_hat = Red_Hat_Display({
  variable: '--font-red-hat',
  subsets: ["latin"],
});

const font = Plus_Jakarta_Sans({
  subsets: ['latin']
})

export const metadata = {
  title: {
    default: `${CompanyName} - Modern Tech & Culture Blog`,
    template: `%s | ${CompanyName}`
  },
  description: "Discover the latest insights in technology, AI, e-commerce, trading, productivity, and lifestyle. Join our community of forward-thinking readers and writers.",
  keywords: ["technology", "AI", "e-commerce", "trading", "productivity", "lifestyle", "blog", "tech news"],
  authors: [{ name: "TechCulture Team" }],
  creator: Company<PERSON>ame,
  publisher: CompanyName,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(BaseUrl),
  alternates: {
    canonical: BaseUrl,
  },
  openGraph: {
    title: `${CompanyName} - Modern Tech & Culture Blog`,
    description: "Discover the latest insights in technology, AI, e-commerce, trading, productivity, and lifestyle.",
    url: BaseUrl,
    siteName: CompanyName,
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Blogs',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: `${CompanyName} - Modern Tech & Culture Blog`,
    description: "Discover the latest insights in technology, AI, e-commerce, trading, productivity, and lifestyle.",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#0066cc" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className={`${red_hat.variable} ${font.className} antialiased min-h-screen bg-background font-sans`}>
        <Toaster />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
