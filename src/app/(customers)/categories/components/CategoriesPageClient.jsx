'use client';

import CategoriesLanding from './CategoriesLanding';
import CategoriesGrid from './CategoriesGrid';
import CategoriesFeatured from './CategoriesFeatured';
import { useCollection } from '@/hooks/useCollection';

export function CategoriesPageClient() {
  const { data: blogs } = useCollection('blogs', {
    expand: 'category',
    sort: '-views',
  });
  const { data: categories } = useCollection('categories', {
    expand: 'tags'
  });


  // Aggregate views by category
  const categoryViews = {};
  blogs?.forEach(blog => {
    const category = blog.expand?.category;
    if (!category) return; // skip blogs without category

    const catId = category.id;
    if (!categoryViews[catId]) {
      categoryViews[catId] = {
        id: catId,
        ...category,
        views: 0
      };
    }
    categoryViews[catId].views += blog.views || 0;
  });

  // Sort categories by total views
  const sortedCategories = Object.values(categoryViews)
    .sort((a, b) => b.views - a.views)
    .slice(0, 3); // take top 3

  return (
    <>
      {/* Hero Section */}
      <CategoriesLanding BLOGS={blogs} categories={categories} />

      {/* Categories Grid */}
      <CategoriesGrid categories={categories} />

      {/* Featured Categories */}
      <CategoriesFeatured featuredCategories={sortedCategories} />
    </>
  );
};
