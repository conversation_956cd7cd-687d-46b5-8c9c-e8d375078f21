import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import pbclient from '@/lib/db';

export default function CategoriesFeatured({ featuredCategories }) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-muted/30"
    >
      <div className="container-modern">
        <div className="text-center mb-12">
          <h2 className="text-headline mb-4">Most Popular Categories</h2>
          <p className="text-muted-foreground text-lg">
            The categories our readers love the most
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {featuredCategories
            .map((data, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <Link href={`/category/${data?.slug}`}>
                  <div className="group cursor-pointer">
                    <div className={`w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br bg-secondary flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <LazyLoadingImage
                        src={pbclient?.files?.getURL(data, data?.icon)}
                        alt={data?.title}
                        className={'object-contain p-3'}
                      />
                    </div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                      {data.title}
                    </h3>
                    <Badge variant="outline" className="group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      #{index + 1} Most Popular
                    </Badge>
                  </div>
                </Link>
              </motion.div>
            ))}
        </div>
      </div>
    </motion.section>
  )
};
