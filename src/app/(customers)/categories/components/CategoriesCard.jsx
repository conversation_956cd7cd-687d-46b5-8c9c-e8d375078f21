import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowUpRight } from 'lucide-react';
import Link from 'next/link';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import pbclient from '@/lib/db';

export default function CategoriesCard({ data, index }) {
  return (
    <motion.div
      key={index}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -8 }}
      className="group"
    >
      <Link href={`/category/${data?.slug}`}>
        <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm cursor-pointer">
          <LazyLoadingImage
            src={pbclient.files.getURL(data, data?.background) || '/placeholder.png'}
            alt={data.title}
            className="h-48 w-full object-cover rounded-t-lg -mt-6 transition-transform duration-700 group-hover:scale-110"
          />

          <CardContent className="px-4">
            <h3 className="text-xl font-semibold">
              {data.title}
            </h3>
            <p className="text-muted-foreground mb-6 line-clamp-3 leading-relaxed">
              {data.description}
            </p>

            {/* Popular Topics */}
            <div className="mb-6">
              <p className="text-sm font-medium mb-3">Popular Topics:</p>
              <div className="flex flex-wrap gap-2">
                {data?.expand?.tags?.slice(0, 3).map((topic, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs"
                  >
                    {topic?.title}
                  </Badge>
                ))}
                {data.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{data.tags.length - 3} more
                  </Badge>
                )}
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full btn-modern group-hover:scale-105 transition-transform duration-300"
              variant="outline"
            >
              Explore {data.title}
              <ArrowUpRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </Link>
    </motion.div>
  )
};
