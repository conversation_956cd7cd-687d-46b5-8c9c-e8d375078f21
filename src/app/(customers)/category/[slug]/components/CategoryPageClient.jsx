'use client';

import { useEffect, useState } from 'react';
import { useIsMobile } from '@/components/ui/use-mobile';
import CategoryHeader from './CategoryHeader';
import CategoryContent from './CategoryContent';
import CategoryFilter from './CategoryFilter';
import CategoryMobileFilter from './CategoryMobileFilter';
import { getAllBlogs } from '@/lib/wrapperFunctions';

export function CategoryPageClient({ categoryData, slug }) {
  // Get Blogs

  const isMobile = useIsMobile();
  const [blogs, setBlogs] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedAuthor, setSelectedAuthor] = useState('All Authors');

  useEffect(() => {
    const getBlogsData = async () => {
      try {
        const blogs_data = await getAllBlogs();
        setBlogs(blogs_data);
      } catch (error) {
        setBlogs([]);
        console.log(error);
      }
    }
    getBlogsData();
  }, []);

  const categoryBlogs = blogs?.filter(blog =>
    blog?.expand?.category?.slug?.toLowerCase() === slug?.toLowerCase()
  );

  // Get unique authors from category blogs
  const categoryAuthors = [...new Set(categoryBlogs?.map(blog => `${blog?.expand?.author?.name}`))];

  // Get all unique tags from blogs in this category
  const availableTags = [...new Set(categoryData?.expand?.tags?.map(tag => tag?.title))];

  // Filter blogs based on search and filters
  const filteredBlogs = categoryBlogs?.filter(blog => {
    const authorName = blog?.expand?.author?.name;
    const matchesSearch = searchQuery === '' ||
      blog?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog?.description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTags = selectedTags.length === 0 ||
      selectedTags.some(tag => blog?.expand?.tags?.filter((t) => t?.title === tag));

    const matchesAuthor = selectedAuthor === 'All Authors' || authorName === selectedAuthor;

    return matchesSearch && matchesTags && matchesAuthor;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <CategoryHeader filteredBlogs={filteredBlogs} categoryData={categoryData} />

      {/* Mobile Filter/Sort Bar */}
      {isMobile && (
        <CategoryMobileFilter
          selectedTags={selectedTags}
          selectedAuthor={selectedAuthor}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          setSelectedTags={setSelectedTags}
          availableTags={availableTags}
          setSelectedAuthor={setSelectedAuthor}
          categoryAuthors={categoryAuthors}
          filteredBlogs={filteredBlogs}
        />
      )}

      {/* Main Content */}
      <div className="container-modern py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 md:gap-6">
          {/* Left Sidebar - Filters - Hidden on mobile */}
          {!isMobile && (
            <CategoryFilter
              selectedTags={selectedTags}
              setSelectedTags={setSelectedTags}
              selectedAuthor={selectedAuthor}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              availableTags={availableTags}
              setSelectedAuthor={setSelectedAuthor}
              categoryAuthors={categoryAuthors}
            />
          )}

          {/* Main Content - Articles */}
          <CategoryContent isMobile={isMobile} filteredBlogs={filteredBlogs} />
        </div>
      </div>
    </div>
  );
};
