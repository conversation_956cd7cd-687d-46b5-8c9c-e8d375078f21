import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Filter,
  Search,
} from 'lucide-react';

export default function CategoryFilter({
  searchQuery,
  setSearchQuery,
  availableTags,
  categoryAuthors,
  selectedTags,
  setSelectedTags,
  selectedAuthor,
  setSelectedAuthor
}) {
  return (
    <div className="lg:col-span-1">
      <div className="bg-card rounded-lg border p-6 sticky top-32">
        {/* Filter & Search Header */}
        <div className="flex items-center gap-2 mb-6">
          <Filter className="w-4 h-4 text-muted-foreground" />
          <span className="font-medium">Filter & Search</span>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search within this category"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Tags */}
        <div className="mb-6">
          <h3 className="font-medium mb-3">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {availableTags.slice(0, 8).map((tag) => (
              <button
                key={tag}
                onClick={() => {
                  setSelectedTags(prev =>
                    prev.includes(tag)
                      ? prev.filter(t => t !== tag)
                      : [...prev, tag]
                  );
                }}
                className={`px-3 py-1 text-xs rounded-full border transition-colors ${selectedTags.includes(tag)
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-muted text-muted-foreground border-border hover:border-primary/50'
                  }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>

        {/* Author Filter */}
        <div className="mb-6">
          <h3 className="font-medium mb-3">Author</h3>
          <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All Authors">All Authors</SelectItem>
              {categoryAuthors.map((author) => (
                <SelectItem key={author} value={author}>{author}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
};
