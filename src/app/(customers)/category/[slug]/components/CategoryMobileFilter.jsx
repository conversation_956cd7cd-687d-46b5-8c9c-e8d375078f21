import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import {
  Filter,
  Search,
  SlidersHorizontal,
} from 'lucide-react';
import { useState } from 'react';

export default function CategoryMobileFilter({
  selectedTags,
  selectedAuthor,
  searchQuery,
  setSearchQuery,
  setSelectedTags,
  availableTags,
  setSelectedAuthor,
  categoryAuthors,
  filteredBlogs
}) {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  return (
    <div className="border-b bg-background/95 backdrop-blur-sm top-[140px] z-30">
      <div className="container-modern py-3">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  <span>Filter</span>
                  {(selectedTags.length > 0 || selectedAuthor !== 'All Authors' || searchQuery) && (
                    <span className="bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {selectedTags.length + (selectedAuthor !== 'All Authors' ? 1 : 0) + (searchQuery ? 1 : 0)}
                    </span>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-full sm:w-80 p-0 flex flex-col">
                <SheetHeader className="p-4 border-b">
                  <SheetTitle className="flex items-center gap-2">
                    <SlidersHorizontal className="w-5 h-5" />
                    Filters
                  </SheetTitle>
                </SheetHeader>
                <div className="flex-1 overflow-y-auto p-4">
                  {/* Search */}
                  <div className="mb-6">
                    <h3 className="font-medium mb-3">Search</h3>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                      <Input
                        placeholder="Search articles..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="mb-6">
                    <h3 className="font-medium mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {availableTags.slice(0, 8).map((tag) => (
                        <button
                          key={tag}
                          onClick={() => {
                            setSelectedTags(prev =>
                              prev.includes(tag)
                                ? prev.filter(t => t !== tag)
                                : [...prev, tag]
                            );
                          }}
                          className={`px-3 py-1 text-xs rounded-full border transition-colors ${selectedTags.includes(tag)
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-muted text-muted-foreground border-border hover:border-primary/50'
                            }`}
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Author Filter */}
                  <div className="mb-6">
                    <h3 className="font-medium mb-3">Author</h3>
                    <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All Authors">All Authors</SelectItem>
                        {categoryAuthors.map((author) => (
                          <SelectItem key={author} value={author}>{author}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="p-4 border-t bg-background">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => setIsMobileFilterOpen(false)}
                    >
                      Close
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={() => setIsMobileFilterOpen(false)}
                    >
                      Show {filteredBlogs.length} Result{filteredBlogs.length !== 1 ? 's' : ''}
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>

          <div className="text-sm text-muted-foreground">
            {filteredBlogs.length} result{filteredBlogs.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </div>
  )
};
