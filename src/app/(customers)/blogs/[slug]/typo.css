/* Custom Typography Plugin Alternative for Tailwind v4 */
@layer components {
  .prose {
    color: theme(colors.foreground);
    max-width: 65ch;
    font-size: 1rem;
    line-height: 1.75;
  }

  .prose :where(p):not(:where([class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where([class~="lead"]):not(:where([class~="not-prose"] *)) {
    color: theme(colors.muted.foreground);
    font-size: 1.25em;
    line-height: 1.6;
    margin-top: 1.2em;
    margin-bottom: 1.2em;
  }

  .prose :where(a):not(:where([class~="not-prose"] *)) {
    color: theme(colors.primary);
    text-decoration: underline;
    font-weight: 500;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    transition: all 0.2s ease;
  }

  .prose :where(a):hover:not(:where([class~="not-prose"] *)) {
    color: theme(colors.primary / 80%);
    text-decoration-thickness: 2px;
  }

  .prose :where(strong):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
  }

  .prose :where(em):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-style: italic;
  }

  .prose :where(code):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    font-size: 0.875em;
    background-color: theme(colors.muted);
    padding: 0.25rem 0.375rem;
    border-radius: 0.375rem;
    border: 1px solid theme(colors.border);
  }

  .prose :where(code):before:not(:where([class~="not-prose"] *)) {
    content: none;
  }

  .prose :where(code):after:not(:where([class~="not-prose"] *)) {
    content: none;
  }

  .prose :where(pre):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    background-color: theme(colors.muted);
    overflow-x: auto;
    font-weight: 400;
    font-size: 0.875em;
    line-height: 1.7142857;
    margin-top: 1.7142857em;
    margin-bottom: 1.7142857em;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    border: 1px solid theme(colors.border);
  }

  .prose :where(pre code):not(:where([class~="not-prose"] *)) {
    background-color: transparent;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-weight: inherit;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
  }

  .prose :where(h1):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 800;
    font-size: 2.25em;
    margin-top: 0;
    margin-bottom: 0.8888889em;
    line-height: 1.1111111;
    scroll-margin-top: 2rem;
  }

  .prose :where(h2):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 700;
    font-size: 1.875em;
    margin-top: 2em;
    margin-bottom: 1em;
    line-height: 1.3333333;
    scroll-margin-top: 2rem;
  }

  .prose :where(h3):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    font-size: 1.5em;
    margin-top: 1.6em;
    margin-bottom: 0.6em;
    line-height: 1.6;
    scroll-margin-top: 2rem;
  }

  .prose :where(h4):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    font-size: 1.25em;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.5;
    scroll-margin-top: 2rem;
  }

  .prose :where(h5):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    font-size: 1.125em;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.5;
    scroll-margin-top: 2rem;
  }

  .prose :where(h6):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    font-size: 1em;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.5;
    scroll-margin-top: 2rem;
  }

  .prose :where(img):not(:where([class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .prose :where(figure):not(:where([class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(figure > *):not(:where([class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(figcaption):not(:where([class~="not-prose"] *)) {
    color: theme(colors.muted.foreground);
    font-size: 0.875em;
    line-height: 1.4285714;
    margin-top: 0.8571429em;
    text-align: center;
  }

  .prose :where(ul):not(:where([class~="not-prose"] *)) {
    list-style-type: disc;
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-left: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"] *)) {
    list-style-type: decimal;
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-left: 1.625em;
  }

  .prose :where(li):not(:where([class~="not-prose"] *)) {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"] *)) {
    position: relative;
  }

  .prose :where(ul > li)::marker:not(:where([class~="not-prose"] *)) {
    color: theme(colors.muted.foreground);
  }

  .prose :where(ol > li)::marker:not(:where([class~="not-prose"] *)) {
    color: theme(colors.muted.foreground);
    font-weight: 400;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"] *)) {
    font-weight: 500;
    font-style: italic;
    color: theme(colors.foreground);
    border-left: 0.25rem solid theme(colors.border);
    quotes: "\201C" "\201D" "\2018" "\2019";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-left: 1em;
    background-color: theme(colors.muted / 20%);
    border-radius: 0 0.5rem 0.5rem 0;
    padding: 1rem 1rem 1rem 1.5rem;
  }

  .prose :where(blockquote p:first-of-type):before:not(:where([class~="not-prose"] *)) {
    content: open-quote;
  }

  .prose :where(blockquote p:last-of-type):after:not(:where([class~="not-prose"] *)) {
    content: close-quote;
  }

  .prose :where(table):not(:where([class~="not-prose"] *)) {
    width: 100%;
    table-layout: auto;
    text-align: left;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: 0.875em;
    line-height: 1.7142857;
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid theme(colors.border);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .prose :where(thead):not(:where([class~="not-prose"] *)) {
    background-color: theme(colors.muted);
    border-bottom: 2px solid theme(colors.border);
  }

  .prose :where(thead th):not(:where([class~="not-prose"] *)) {
    color: theme(colors.foreground);
    font-weight: 600;
    vertical-align: bottom;
    padding: 1rem 0.75rem;
    border-right: 1px solid theme(colors.border);
    border-bottom: 1px solid theme(colors.border);
    position: relative;
  }

  .prose :where(thead th:first-child):not(:where([class~="not-prose"] *)) {
    border-top-left-radius: 0.75rem;
  }

  .prose :where(thead th:last-child):not(:where([class~="not-prose"] *)) {
    border-top-right-radius: 0.75rem;
    border-right: none;
  }

  .prose :where(tbody tr):not(:where([class~="not-prose"] *)) {
    border-bottom: 1px solid theme(colors.border);
    transition: background-color 0.15s ease-in-out;
  }

  .prose :where(tbody tr:hover):not(:where([class~="not-prose"] *)) {
    background-color: theme(colors.muted / 30%);
  }

  .prose :where(tbody tr:last-child):not(:where([class~="not-prose"] *)) {
    border-bottom-width: 0;
  }

  .prose :where(tbody tr:last-child td:first-child):not(:where([class~="not-prose"] *)) {
    border-bottom-left-radius: 0.75rem;
  }

  .prose :where(tbody tr:last-child td:last-child):not(:where([class~="not-prose"] *)) {
    border-bottom-right-radius: 0.75rem;
  }

  .prose :where(tbody td):not(:where([class~="not-prose"] *)) {
    vertical-align: baseline;
    padding: 0.875rem 0.75rem;
    border-right: 1px solid theme(colors.border);
  }

  .prose :where(tbody td:last-child):not(:where([class~="not-prose"] *)) {
    border-right: none;
  }

  .prose :where(tfoot):not(:where([class~="not-prose"] *)) {
    border-top: 2px solid theme(colors.border);
    background-color: theme(colors.muted / 50%);
  }

  .prose :where(tfoot td):not(:where([class~="not-prose"] *)) {
    vertical-align: top;
    padding: 0.875rem 0.75rem;
    border-right: 1px solid theme(colors.border);
    font-weight: 500;
  }

  .prose :where(tfoot td:last-child):not(:where([class~="not-prose"] *)) {
    border-right: none;
  }

  .prose :where(hr):not(:where([class~="not-prose"] *)) {
    border-color: theme(colors.border);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
  }

  /* Size variants */
  .prose-sm {
    font-size: 0.875rem;
    line-height: 1.7142857;
  }

  .prose-sm :where(p):not(:where([class~="not-prose"] *)) {
    margin-top: 1.1428571em;
    margin-bottom: 1.1428571em;
  }

  .prose-sm :where(h1):not(:where([class~="not-prose"] *)) {
    font-size: 2.1428571em;
    margin-top: 0;
    margin-bottom: 0.8em;
    line-height: 1.2;
  }

  .prose-sm :where(h2):not(:where([class~="not-prose"] *)) {
    font-size: 1.7142857em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.3333333;
  }

  .prose-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .prose-lg :where(p):not(:where([class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .prose-lg :where(h1):not(:where([class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .prose-xl {
    font-size: 1.25rem;
    line-height: 1.8;
  }

  .prose-xl :where(p):not(:where([class~="not-prose"] *)) {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
  }

  .prose-xl :where(h1):not(:where([class~="not-prose"] *)) {
    font-size: 2.8em;
    margin-top: 0;
    margin-bottom: 0.8571429em;
    line-height: 1;
  }

  /* Dark mode adjustments */
  .dark .prose {
    color: theme(colors.foreground);
  }

  .dark .prose :where(blockquote):not(:where([class~="not-prose"] *)) {
    background-color: theme(colors.muted / 10%);
  }

  /* Syntax highlighting support */
  .prose :where(.hljs):not(:where([class~="not-prose"] *)) {
    background: theme(colors.muted) !important;
    border: 1px solid theme(colors.border);
  }

  /* Copy code button positioning */
  .prose :where(pre):not(:where([class~="not-prose"] *)) {
    position: relative;
  }

  /* Not prose utility */
  .not-prose {
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    margin-top: inherit;
    margin-bottom: inherit;
  }

  .not-prose * {
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    margin-top: inherit;
    margin-bottom: inherit;
  }
}
