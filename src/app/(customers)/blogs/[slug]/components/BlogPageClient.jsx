'use client';

import { useEffect, useState } from 'react';
import { markdownAsHtml2 } from '@/utils/markdownConverter';
import OnThisPage from '@/components/blocks/on-this-page';
import pbclient from '@/lib/db';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import { CompanyName } from '@/constants/companyName';
import { Button } from '@/components/ui/button';
import { ArrowUpRight } from 'lucide-react';
import Link from 'next/link';
import PageLoader from '@/components/blocks/PageLoader';
import { colorScheme } from '@/constants/colors';
import BlogsList from './BlogsList';
import { ReadingProgress } from '@/components/ui/ReadingProgress';
import extractNumberFromTimeText from '@/utils/extractNumberFromTimeText';

export default function BlogPageClient({ blog, blogs, slug }) {
  const [htmlContent, setHtmlContent] = useState('');
  const [pageLoading, setPageLoading] = useState(true);

  useEffect(() => {
    if (!slug) return;
    const viewIncrement = async () => {
      try {
        const urls = JSON.parse(localStorage.getItem('viewedArticles')) || [];
        let flag = true;
        urls?.map(async (url) => {
          if (url === slug) {
            flag = false;
          }
        });

        if (flag) {
          await pbclient.collection('blogs').update(blog?.id, {
            views: blog?.views + 1
          });
          urls?.push(slug)
          localStorage.setItem('viewedArticles', JSON.stringify(urls));
        }

      } catch (error) {
        console.log(error)
      }
    }
    const run = async () => {
      try {
        await viewIncrement();

        // Build file URL for the markdown content
        let html = '';
        if (blog?.content) {
          const contentUrl = pbclient.files.getURL(blog, blog.content);
          const parsed = await markdownAsHtml2({ url: contentUrl, title: blog.title });
          html = parsed?.html || '';
        }
        setHtmlContent(html);
        setPageLoading(false);
      } catch (err) {
        console.error('Failed to load blog preview:', err);
      }
    };
    run();
  }, [slug]);

  if (pageLoading) {
    return <PageLoader variant="dots" color={colorScheme?.primary} size={60} />
  }

  return (
    <>
      <ReadingProgress estimatedReadTime={extractNumberFromTimeText(blog?.readTime) || 10} />
      <div className='flex gap-8 items-start pb-6 border-b'>
        <div className="prose dark:prose-invert max-w-3xl mx-auto p-4">
          <LazyLoadingImage
            src={pbclient.files.getURL(blog, blog?.thumbnail) || '/placeholder.png'}
            alt={'thumbnail'}
            className={'min-w-xl'}
          />
          <h1 className="text-4xl font-bold mb-4">{blog.title}</h1>
          <p className="text-base mb-2 border-l-4 border-gray-500 pl-4 italic">&quot;{blog.description}&quot;</p>
          <div className="flex gap-2">
            <p className="text-sm text-gray-500 mb-4 italic">By {blog?.expand?.author?.name || ''}</p>
            <p className="text-sm text-gray-500 mb-4">{new Date(blog?.created).toLocaleString()}</p>
          </div>
          <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
        </div>
        <OnThisPage htmlContent={htmlContent} />
      </div>
      <div className='p-6 max-w-6xl mx-auto space-y-4'>
        <h1 className='text-3xl font-semibold'>More articles from {CompanyName}</h1>
        <BlogsList blogs={blogs?.sort(() => Math.random() - 0.5)} />
        <Link href={`/search/?categories=${blog?.expand?.category?.title}`}>
          <Button variant={'outline'} className={'w-full'}>
            <p>See more recommedtaions</p>
            <ArrowUpRight />
          </Button>
        </Link>
      </div>
    </>
  )
};
