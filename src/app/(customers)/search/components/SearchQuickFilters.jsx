'use client';

import { motion, AnimatePresence } from 'motion/react';
import { X, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';
import { getAllCategories } from '@/lib/wrapperFunctions';

export function SearchQuickFilters({
  categories,
  filters,
  onFilterChange,
  searchQuery,
  onSearchChange,
  activeFiltersCount,
  onClearAll
}) {
  const handleQuickCategoryFilter = (category) => {
    const isSelected = filters.categories.includes(category);
    const newCategories = isSelected
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];

    onFilterChange({
      ...filters,
      categories: newCategories
    });
  };

  const removeFilter = (type, value) => {
    switch (type) {
      case 'search':
        onSearchChange('');
        break;
      case 'category':
        onFilterChange({
          ...filters,
          categories: filters.categories.filter(c => c !== value)
        });
        break;
      case 'author':
        onFilterChange({
          ...filters,
          authors: filters.authors.filter(a => a !== value)
        });
        break;
      case 'tag':
        onFilterChange({
          ...filters,
          tags: filters.tags.filter(t => t !== value)
        });
        break;
      case 'date':
        onFilterChange({
          ...filters,
          dateRange: 'all'
        });
        break;
      case 'format':
        onFilterChange({
          ...filters,
          format: 'all'
        });
        break;
    }
  };

  const getDateLabel = (dateRange) => {
    const dateOptions = {
      'all': 'All Time',
      'today': 'Today',
      'week': 'This Week',
      'month': 'This Month',
      'year': 'This Year'
    };
    return dateOptions[dateRange] || dateRange;
  };

  const getFormatLabel = (format) => {
    const formatOptions = {
      'all': 'All Formats',
      'article': 'Article',
      'tutorial': 'Tutorial',
      'guide': 'Guide',
      'review': 'Review'
    };
    return formatOptions[format] || format;
  };

  return (
    <div className="border-b bg-muted/30">
      <div className="container-modern py-4">
        {/* Quick Category Filters */}
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-3">Quick filters:</h3>
            <div className="flex flex-wrap gap-2">
              {categories?.map((category, index) => (
                <Button
                  key={index}
                  variant={filters.categories.includes(category?.title) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleQuickCategoryFilter(category?.title)}
                  className="text-xs h-8"
                >
                  {category?.title}
                </Button>
              ))}
            </div>
          </div>

          {/* Active Filters */}
          <AnimatePresence>
            {activeFiltersCount > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-3"
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Active filters ({activeFiltersCount}):
                  </h4>
                  <Button variant="ghost" size="sm" onClick={onClearAll} className="text-xs">
                    Clear all
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {/* Search Query */}
                  {searchQuery && (
                    <Badge variant="secondary" className="gap-1 pr-1">
                      Search: "{searchQuery.length > 20 ? searchQuery.substring(0, 20) + '...' : searchQuery}"
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('search')}
                        aria-label="Remove search filter"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  )}

                  {/* Categories */}
                  {filters.categories.map((category) => (
                    <Badge key={category} variant="secondary" className="gap-1 pr-1">
                      Category: {category}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('category', category)}
                        aria-label={`Remove ${category} filter`}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  ))}

                  {/* Authors */}
                  {filters.authors.map((author) => (
                    <Badge key={author} variant="secondary" className="gap-1 pr-1">
                      Author: {author}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('author', author)}
                        aria-label={`Remove ${author} filter`}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  ))}

                  {/* Tags */}
                  {filters.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="gap-1 pr-1">
                      <Tag className="w-3 h-3" />
                      {tag}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('tag', tag)}
                        aria-label={`Remove ${tag} tag filter`}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  ))}

                  {/* Date Range */}
                  {filters.dateRange !== 'all' && (
                    <Badge variant="secondary" className="gap-1 pr-1">
                      Date: {getDateLabel(filters.dateRange)}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('date')}
                        aria-label="Remove date filter"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  )}

                  {/* Format */}
                  {filters.format !== 'all' && (
                    <Badge variant="secondary" className="gap-1 pr-1">
                      Format: {getFormatLabel(filters.format)}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => removeFilter('format')}
                        aria-label="Remove format filter"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  )}

                  {/* Reading Time */}
                  {(filters.readTimeRange[0] > 0 || filters.readTimeRange[1] < 30) && (
                    <Badge variant="secondary" className="gap-1 pr-1">
                      Read time: {filters.readTimeRange[0]}-{filters.readTimeRange[1]} min
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 w-4 h-4 hover:text-destructive ml-1"
                        onClick={() => onFilterChange({
                          ...filters,
                          readTimeRange: [0, 30]
                        })}
                        aria-label="Remove reading time filter"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
