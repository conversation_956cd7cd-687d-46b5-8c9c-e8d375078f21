'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Filter, ChevronDown, ChevronUp, User, Calendar, Clock, Tag, } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { dateOptions } from '@/constants/filter';
import { getAllTags } from '@/lib/wrapperFunctions';

export function SearchFilters({ filters, onFilterChange, isMobile = false, blogs, categories }) {
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    authors: true,
    date: true,
    readTime: false,
    tags: true
  });
  const [tags, setTags] = useState([]);
  useEffect(() => {
    const getData = async () => {
      try {
        const data = await getAllTags();
        setTags(data);
      } catch (error) {
        setTags([]);
        console.log(error);
      }
    }
    getData();
  }, []);

  // Get unique authors from all blogs
  const allAuthors = [...new Set(blogs?.map(blog => blog?.expand?.author?.name))];

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category, checked) => {
    const newCategories = checked
      ? [...filters.categories, category]
      : filters.categories.filter(c => c !== category);

    onFilterChange({
      ...filters,
      categories: newCategories
    });
  };

  const handleAuthorChange = (author, checked) => {
    const newAuthors = checked
      ? [...filters.authors, author]
      : filters.authors.filter(a => a !== author);

    onFilterChange({
      ...filters,
      authors: newAuthors
    });
  };

  const handleTagChange = (tag, checked) => {
    const newTags = checked
      ? [...filters.tags, tag]
      : filters.tags.filter(t => t !== tag);

    onFilterChange({
      ...filters,
      tags: newTags
    });
  };

  const handleDateChange = (newDate) => {
    onFilterChange({
      ...filters,
      dateRange: newDate
    });
  };

  const handleReadTimeChange = (newRange) => {
    onFilterChange({
      ...filters,
      readTimeRange: newRange
    });
  };

  const clearAllFilters = () => {
    onFilterChange({
      categories: [],
      authors: [],
      tags: [],
      dateRange: 'all',
      format: 'all',
      readTimeRange: [0, 30],
      sortBy: 'relevance'
    });
  };

  const activeFiltersCount = filters.categories.length + filters.authors.length + filters.tags.length +
    (filters.dateRange !== 'all' ? 1 : 0) + (filters.format !== 'all' ? 1 : 0) +
    (filters.readTimeRange[0] > 0 || filters.readTimeRange[1] < 30 ? 1 : 0);

  const FilterSection = ({ title, icon: Icon, section, children }) => (
    <div className="border-b border-border/50 last:border-b-0">
      <button
        onClick={() => toggleSection(section)}
        className="w-full flex items-center justify-between p-4 hover:bg-muted/50 transition-colors"
      >
        <div className="flex items-center gap-2">
          <Icon className="w-4 h-4 text-muted-foreground" />
          <span className="font-medium text-sm">{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        )}
      </button>
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return (
    <Card className={isMobile ? "w-full border-0 shadow-none" : "sticky top-32"}>
      {!isMobile && (
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between text-base">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4" />
              Filters
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2 px-1.5 py-0.5 text-xs">
                  {activeFiltersCount}
                </Badge>
              )}
            </div>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" size="sm" onClick={clearAllFilters} className="text-xs">
                Clear All
              </Button>
            )}
          </CardTitle>
        </CardHeader>
      )}

      {/* Mobile Quick Filters */}
      {isMobile && (
        <div className="mb-4 px-4">
          <h3 className="text-sm font-medium text-muted-foreground mb-3">Quick filters:</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={filters.categories.includes(category?.title) ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryChange(category?.title, !filters.categories.includes(category?.title))}
                className="text-xs h-8"
              >
                {category?.title}
              </Button>
            ))}
          </div>
          {activeFiltersCount > 0 && (
            <div className="mt-4 flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} active
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs h-7 px-2"
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
      )}

      <CardContent className={isMobile ? "p-0" : "p-0"}>

        {/* Authors */}
        <FilterSection title="Authors" icon={User} section="authors">
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {allAuthors.slice(0, 10).map((author) => (
              <div key={author} className="flex items-center space-x-2">
                <Checkbox
                  id={`author-${author}`}
                  checked={filters.authors.includes(author)}
                  onCheckedChange={(checked) => handleAuthorChange(author, checked)}
                />
                <label
                  htmlFor={`author-${author}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {author}
                </label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Date Range */}
        <FilterSection title="Date" icon={Calendar} section="date">
          <Select value={filters.dateRange} onValueChange={handleDateChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {dateOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Reading Time */}
        {/*
        <FilterSection title="Reading Time" icon={Clock} section="readTime">
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              {filters.readTimeRange[0]}-{filters.readTimeRange[1]} minutes
            </div>
            <Slider
              value={filters.readTimeRange}
              onValueChange={handleReadTimeChange}
              max={30}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        </FilterSection>
        */}

        {/* Tags */}
        <FilterSection title="Tags" icon={Tag} section="tags">
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {tags.map((tag, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  id={`tag-${tag?.title}`}
                  checked={filters.tags.includes(tag?.title)}
                  onCheckedChange={(checked) => handleTagChange(tag?.title, checked)}
                />
                <label
                  htmlFor={`tag-${tag?.title}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {tag?.title}
                </label>
              </div>
            ))}
          </div>
        </FilterSection>
      </CardContent>
    </Card>
  );
}
