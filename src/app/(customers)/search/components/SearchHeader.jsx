import { Search, ArrowLeft, X } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function SearchHeader({ searchQuery, filteredBlogs, handleSearchChange, }) {
  return (
    <div className="border-b bg-background/95 backdrop-blur-sm sticky top-16 md:top-20 z-40">
      <div className="container-modern py-4 md:py-6">
        <div className="flex items-center gap-3 mb-4 md:mb-6">
          <Link href="/home" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Back to Home</span>
            <span className="sm:hidden">Back</span>
          </Link>
        </div>

        {/* Search Header */}
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">
              {searchQuery ? `Search results for "${searchQuery}"` : 'Search Articles'}
            </h1>
            <p className="text-muted-foreground text-sm md:text-base">
              {searchQuery ? `Found ${filteredBlogs.length} results` : 'Discover articles across all categories'}
            </p>
          </div>

          {/* Search Bar */}
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
            <Input
              placeholder="Search articles, topics, or authors..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-12 pr-4 py-3 text-sm md:text-base bg-background border-2 border-border focus:border-primary/50 rounded-xl"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 h-8 w-8"
                onClick={() => handleSearchChange('')}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
};
