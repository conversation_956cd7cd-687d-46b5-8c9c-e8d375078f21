'use client';

import { useState } from 'react';
import { Newsletter } from '@/components/sections/Newsletter';
import AuthorsLanding from './AuthorsLanding';
import AuthorsFilter from './AuthorsFilter';
import AuthorsGrid from './AuthorsGrid';

export default function AuthorsPageClient() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedExpertise, setSelectedExpertise] = useState('');
  const [filteredAuthors, setFilteredAuthors] = useState([]);

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <AuthorsLanding />

      {/* Filters Section */}
      <AuthorsFilter
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedExpertise={selectedExpertise}
        setSelectedExpertise={setSelectedExpertise}
        filteredAuthors={filteredAuthors}
        setFilteredAuthors={setFilteredAuthors}
      />

      {/* Authors Grid */}
      <AuthorsGrid
        filteredAuthors={filteredAuthors}
        setSearchQuery={setSearchQuery}
        setSelectedExpertise={setSelectedExpertise}
      />
    </div>
  );
};
