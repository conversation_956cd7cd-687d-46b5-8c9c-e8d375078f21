import { motion } from 'motion/react';
import { Search, } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useEffect, useMemo, useState } from 'react';
import { getAllUsers } from '@/lib/wrapperFunctions';

export default function AuthorsFilter({
  searchQuery,
  setSearchQuery,
  selectedExpertise,
  setSelectedExpertise,
  filteredAuthors,
  setFilteredAuthors
}) {
  const [authors, setAuthors] = useState([]);
  useEffect(() => {
    const getData = async () => {
      try {
        const data = await getAllUsers();
        console.log(data);
        setAuthors(data?.filter((entry) => entry?.role === 'Author'));
      } catch (error) {
        setAuthors([]);
        console.log(error);
      }
    }
    getData();
  }, []);


  // Get all unique expertise areas
  const allExpertise = useMemo(() => {
    const expertiseSet = new Set();
    authors.forEach(author => {
      author?.expand?.profile?.expertise.forEach(exp => expertiseSet.add(exp));
    });
    return Array.from(expertiseSet).sort();
  }, [authors]);

  // Filter and sort authors
  const filtered_authors = useMemo(() => {
    let filtered = authors.filter(author => {
      const matchesSearch = searchQuery === '' ||
        author?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        author?.expand?.profile?.bio.toLowerCase().includes(searchQuery.toLowerCase()) ||
        author?.expand?.profile?.expertise.some(exp => exp.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesExpertise = selectedExpertise === '' ||
        author?.expand?.profile?.expertise.includes(selectedExpertise);

      return matchesSearch && matchesExpertise;
    });
    return filtered;
  }, [authors, searchQuery, selectedExpertise]);

  useEffect(() => {
    setFilteredAuthors(filtered_authors);
  }, [filtered_authors]);

  return (
    <motion.section
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-muted/30"
    >
      <div className="container-modern">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-xl">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="Search authors by name, expertise, or bio..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 w-full"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-3 items-center">
              <div className="flex items-center gap-2">
                <select
                  value={selectedExpertise}
                  onChange={(e) => setSelectedExpertise(e.target.value)}
                  className="h-10 px-3 rounded-md border border-input bg-background text-sm min-w-[150px]"
                >
                  <option value="">All Expertise</option>
                  {allExpertise.map(expertise => (
                    <option key={expertise} value={expertise}>{expertise}</option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <p className="text-muted-foreground">
                Showing {filteredAuthors.length} of {authors.length} authors
              </p>
            </div>
          </div>

          {/* Results count */}
        </div>
      </div>
    </motion.section>
  )
};
