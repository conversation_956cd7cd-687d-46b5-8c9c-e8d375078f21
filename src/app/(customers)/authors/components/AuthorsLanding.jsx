import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import {
  Users,
} from 'lucide-react';

export default function AuthorsLanding() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="section-modern min-h-[70dvh] primary-alt-gradient flex items-center justify-center"
    >
      <div className="container-modern mt-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Badge variant="outline" className="px-4 py-2 text-sm mb-6">
              <Users className="w-4 h-4 mr-2" />
              Our Authors
            </Badge>
            <h1 className="text-display mb-2 text-shimmer">
              Meet Our
            </h1>
            <h1 className="text-display mb-8 text-hot-shimmer">Expert Authors</h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Discover the brilliant minds behind TechCulture's content. Our diverse team of writers,
              technologists, and thought leaders brings you insights from the cutting edge of innovation.
            </p>
          </motion.div>
        </div>
      </div>
    </motion.section>
  )
};
