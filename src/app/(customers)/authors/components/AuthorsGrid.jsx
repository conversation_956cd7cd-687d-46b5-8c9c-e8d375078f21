import { motion } from 'motion/react';
import { Card, CardContent, } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Search,
  ArrowUpRight,
} from 'lucide-react';
import Link from 'next/link';
import slugify from '@/utils/slugify';
import pbclient from '@/lib/db';

export default function AuthorsGrid({ filteredAuthors, setSearchQuery, setSelectedExpertise, }) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="mt-4"
    >
      <div className="container-modern">
        {filteredAuthors.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-16"
          >
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No authors found</h3>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search criteria or filters to find more authors.
            </p>
            <Button
              onClick={() => {
                setSearchQuery('');
                setSelectedExpertise('');
              }}
              variant="outline"
            >
              Clear Filters
            </Button>
          </motion.div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredAuthors.map((author, index) => (
              <motion.div
                key={author.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
                  <div className="relative -mt-6">
                    <div className="h-32 bg-gradient-to-br from-primary/10 to-primary/5"></div>
                    <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
                      <Avatar className="w-24 h-24 ring-4 ring-background group-hover:ring-primary/20 transition-all duration-300">
                        <AvatarImage src={pbclient.files.getURL(author, author.avatar)} alt={`${author.name}`} />
                        <AvatarFallback className="text-lg">
                          {author.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </div>

                  <CardContent className="pt-16 pb-6 text-center">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                      {author.name}
                    </h3>
                    <Badge variant="secondary" className="mb-4">
                      {author?.expand?.profile?.workRole}
                    </Badge>

                    <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
                      {author?.expand?.profile?.bio}
                    </p>

                    {/* Expertise tags */}
                    <div className="flex flex-wrap gap-1 justify-center mb-4">
                      {author?.expand?.profile?.expertise?.slice(0, 2).map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {author?.expand?.profile?.expertise?.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{author?.expand?.profile?.expertise?.length - 2} more
                        </Badge>
                      )}
                    </div>

                    {/* Action button */}
                    <Link href={`/authors/${slugify(author?.name)}`}>
                      <Button className="w-full btn-modern group">
                        View Profile
                        <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.section>
  )
};
