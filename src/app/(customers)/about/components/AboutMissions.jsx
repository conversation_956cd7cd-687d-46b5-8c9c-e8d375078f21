import LazyLoadingImage from "@/components/blocks/LazyLoadingImage";
import { Users, Star, Quote } from "lucide-react"
import { motion } from "motion/react";

export default function AboutMissions() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-muted/30"
    >
      <div className="container-modern">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4"
            >
              Our Mission
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg"
            >
              Empowering voices and connecting minds through exceptional content
            </motion.p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold mb-6">What We Believe</h3>
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Star className="w-6 h-6 text-purple-500" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Quality First</h4>
                    <p className="text-muted-foreground">
                      We maintain the highest standards while fostering an inclusive environment where diverse perspectives thrive.
                    </p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Quote className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Democratizing Knowledge</h4>
                    <p className="text-muted-foreground">
                      Every person has unique insights to share. We provide the platform and community to amplify these voices.
                    </p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Users className="w-6 h-6 text-green-500" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Building Community</h4>
                    <p className="text-muted-foreground">
                      We foster meaningful connections between writers and readers, creating a space for authentic dialogue.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-primary/10 to-primary/5 rounded-3xl overflow-hidden">
                <LazyLoadingImage
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=600&fit=crop"
                  alt="Our mission - team collaboration"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
              </div>

              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 bg-background rounded-2xl p-4 shadow-xl border">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">50K+ Readers</span>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 bg-background rounded-2xl p-4 shadow-xl border">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">500+ Articles</span>
                </div>
              </div>

            </motion.div>
          </div>
        </div>
      </div>
    </motion.section>
  )
};
