"use client"

import * as React from "react"

export function TeamSwitcher({
  teams,
}) {

  return (
    <div
      className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground flex items-center gap-2 p-2"
    >
      <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
        <teams.logo className="size-4" />
      </div>
      <div className="grid flex-1 text-left leading-tight">
        <span className="truncate font-medium">{teams.name}</span>
      </div>
    </div>
  )
};
