import { useState } from "react";
import { Upload, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";
import { DynamicMultiDatalist } from "@/components/ui/data-multi-list";
import slugify from "@/utils/slugify";

export default function Form() {
  const { createItem, mutation } = useCollection('categories');
  const { data: listTags } = useCollection('tags');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    icon: null,
    background: null,
  });
  const [tags, setTags] = useState([]);

  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value, files, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'file' ? files?.[0] : value,
    }));
  };

  const handleReset = () => {
    setFormData({
      title: '',
      description: '',
      tags: [],
      icon: null,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = new FormData();
      if (formData.icon) payload.append('icon', formData.icon);
      if (formData.background) payload.append('background', formData.background);
      payload.append('title', formData.title || '');
      payload.append('description', formData.description || '');
      payload.append('slug', slugify(formData.title) || '');
      payload.append('tags', JSON.stringify((tags || []).filter(Boolean)));

      const newCategory = await createItem(payload);
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.CREATE,
        module: `Categories`,
        details: newCategory
      });
      toast.success('Added the new category');
    } catch (error) {
      console.log(error)
      toast.error(error.message);
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      className='max-w-2xl'
    >
      <DialogTrigger asChild>
        <Button
          className='rounded-md text-sm'
        >
          <p>Add Category</p>
          <Plus className='w-5 h-5' />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Categories</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter category title"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Short description"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Icon (image)</Label>
            <Input
              type="file"
              accept="image/*"
              name="icon"
              onChange={handleChange}
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Background (image)</Label>
            <Input
              type="file"
              accept="image/*"
              name="background"
              onChange={handleChange}
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Tags</Label>
            <DynamicMultiDatalist
              items={listTags || []}
              label="Tags"
              getLabel={(c) => c?.title}
              placeholder="Select tag(s)..."
              onChange={(selected) => setTags(selected.map((c) => c.id))}
            />
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
            >
              <p>Add Categories</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
