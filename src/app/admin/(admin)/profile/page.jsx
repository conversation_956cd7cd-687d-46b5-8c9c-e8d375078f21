'use client'
import { useState, useCallback, useMemo } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCollection } from "@/hooks/useCollection";
import ProfileHeader from "./components/ProfileHeader";
import ProfileOverview from "./components/ProfileOverview";

import ProfileEducationSection from "./components/sections/ProfileEducationSection";
import ProfileExperienceSection from "./components/sections/ProfileExperienceSection";
import ProfileExpertiseSection from "./components/sections/ProfileExpertiseSection";
import ProfileSpecialisationSection from "./components/sections/ProfileSpecialisationSection";
import ProfileAchievementsSection from "./components/sections/ProfileAchievementsSection";
import ProfileLanguagesSection from "./components/sections/ProfileLanguagesSection";
import ProfileSocialsSection from "./components/sections/ProfileSocialsSection";

export default function ProfilePage() {
  const { user } = useAuth();
  const [currentUser, setCurrentUser] = useState(user);

  // Fetch user profile data
  const {
    data: userProfile,
    loading: profileLoading,
    mutation: refreshProfile,
    updateItem: updateProfile,
  } = useCollection('user_profiles', {
    filter: `user.id = "${user?.id}"`,
    expand: 'user'
  });

  const handleUserUpdate = useCallback(() => {
    refreshProfile();
  }, [refreshProfile]);

  const currentProfile = useMemo(() => userProfile?.[0] || null, [userProfile]);

  return (
    <div className="min-h-screen bg-secondary dark:bg-background p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Profile Header with Avatar */}
        <ProfileHeader
          user={currentUser}
          onUserUpdate={handleUserUpdate}
          userProfile={currentProfile}
        />

        {/* Profile Overview Section */}
        <ProfileOverview
          user={currentUser}
          userProfile={currentProfile}
          onUserUpdate={handleUserUpdate}
          refreshProfile={refreshProfile}
          loading={profileLoading}
        />

        {/* Profile Detail Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left */}
          <div className="space-y-6">
            {/* Experience & Education */}
            <ProfileExperienceSection
              profileId={currentProfile?.id}
              value={currentProfile?.experience || []}
              onSubmit={updateProfile}
            />

            <ProfileEducationSection
              profileId={currentProfile?.id}
              value={currentProfile?.education || []}
              onSubmit={updateProfile}
            />
          </div>

          {/* Right */}
          <div className="space-y-6">
            <ProfileExpertiseSection
              profileId={currentProfile?.id}
              value={currentProfile?.expertise || []}
              onSubmit={updateProfile}
            />

            <ProfileSpecialisationSection
              profileId={currentProfile?.id}
              value={currentProfile?.specialisation || []}
              onSubmit={updateProfile}
            />

            <ProfileAchievementsSection
              profileId={currentProfile?.id}
              value={currentProfile?.achievements || []}
              onSubmit={updateProfile}
            />

            <ProfileLanguagesSection
              profileId={currentProfile?.id}
              value={currentProfile?.languages || []}
              onSubmit={updateProfile}
            />

            <ProfileSocialsSection
              profileId={currentProfile?.id}
              value={currentProfile?.socials || {}}
              onSubmit={updateProfile}
            />
          </div>
        </div>
      </div>
    </div>
  )
};
