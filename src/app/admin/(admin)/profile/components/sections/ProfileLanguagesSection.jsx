'use client'
import ProfileSectionCard from './ProfileSectionCard';
import { Languages } from 'lucide-react';
import ListInput from '../inputs/ListInput';

export default function ProfileLanguagesSection({ profileId, value = [], onSubmit }) {
  return (
    <ProfileSectionCard title="Languages" icon={Languages}>
      <ListInput
        label="Add Language"
        items={value || []}
        placeholder="e.g., English"
        addLabel="Add"
        profileId={profileId}
        onSubmit={onSubmit}
        headerValue='languages'
      />
    </ProfileSectionCard>
  );
}
