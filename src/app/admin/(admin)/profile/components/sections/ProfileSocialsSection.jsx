'use client'
import { useState, useEffect } from 'react';
import ProfileSectionCard from './ProfileSectionCard';
import { Share2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

export default function ProfileSocialsSection({ profileId, value = {}, onSubmit }) {
  const [socials, setSocials] = useState(value || {});

  useEffect(() => {
    setSocials(value || {});
  }, [value]);

  const update = (key, val) => {
    const next = { ...socials, [key]: val };
    setSocials(next);
  };

  const platforms = [
    { key: 'twitter', label: 'Twitter' },
    { key: 'linkedin', label: 'LinkedIn' },
    { key: 'github', label: 'GitHub' },
    { key: 'website', label: 'Website' },
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await onSubmit(profileId, {
        socials: socials
      });
      toast.success('Social Links updated.')
    } catch (error) {
      toast.error('Error updating Social Links');
      console.log(error);
    }
  }

  return (
    <ProfileSectionCard title="Socials" icon={Share2}>
      <div className="space-y-3">
        {platforms.map(({ key, label }) => (
          <div key={key} className="grid md:grid-cols-4 gap-3 items-center">
            <label className="text-sm text-secondary-foreground/60 md:col-span-1">{label}</label>
            <Input
              className="md:col-span-3"
              value={socials?.[key] || ''}
              onChange={(e) => update(key, e.target.value)}
              placeholder={`Enter ${label} URL`}
            />
          </div>
        ))}
        {socials !== value && <Button onClick={handleSubmit}>Save Changes</Button>}
      </div>
    </ProfileSectionCard>
  );
}
