'use client'
import { useState, useEffect } from 'react';
import ProfileSectionCard from './ProfileSectionCard';
import { GraduationCap, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function ProfileEducationSection({ profileId, value = [], onSubmit }) {
  const [list, setList] = useState(value || []);

  useEffect(() => {
    setList(value || []);
  }, [value]);

  const add = () => setList(prev => [...prev, { degree: '', institution: '', year: '' }]);

  const update = (idx, key, val) => {
    const next = list.map((item, i) => i === idx ? { ...item, [key]: val } : item);
    setList(next);
  };

  const remove = (idx) => {
    const next = list.filter((_, i) => i !== idx);
    setList(next);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await onSubmit(profileId, {
        education: list
      });
      toast.success('Education added.')
    } catch (error) {
      toast.error('Error updating Education');
      console.log(error);
    }
  }

  return (
    <ProfileSectionCard title="Education" icon={GraduationCap}>
      <div className="space-y-4">
        {list?.map((edu, idx) => (
          <div key={idx} className="grid gap-3 p-3 border-2 rounded-lg bg-muted/50">
            <Input value={edu.degree} onChange={(e) => update(idx, 'degree', e.target.value)} placeholder="Degree" />
            <Input value={edu.institution} onChange={(e) => update(idx, 'institution', e.target.value)} placeholder="Institution" />
            <Input value={edu.year} onChange={(e) => update(idx, 'year', e.target.value)} placeholder="Year" />
            <div className="flex justify-end">
              <Button type="button" variant="ghost" className="text-destructive" onClick={() => remove(idx)}>Remove</Button>
            </div>
          </div>
        ))}
        <div className='flex items-center justify-between'>
          <Button
            type="button"
            onClick={add}
            variant={'outline'}
          >
            Add <Plus size={18} />
          </Button>
          {list !== value && <Button onClick={handleSubmit}>Save Changes</Button>}
        </div>
      </div>
    </ProfileSectionCard>
  );
}
