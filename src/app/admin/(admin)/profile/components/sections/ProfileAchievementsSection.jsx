'use client'
import ProfileSectionCard from './ProfileSectionCard';
import { Award } from 'lucide-react';
import ListInput from '../inputs/ListInput';

export default function ProfileAchievementsSection({ profileId, value = [], onSubmit }) {
  return (
    <ProfileSectionCard title="Achievements" icon={Award}>
      <ListInput
        label="Add Achievement"
        items={value || []}
        placeholder="e.g., Won XYZ Award"
        addLabel="Add"
        profileId={profileId}
        onSubmit={onSubmit}
        headerValue='achievements'
      />
    </ProfileSectionCard>
  );
}
