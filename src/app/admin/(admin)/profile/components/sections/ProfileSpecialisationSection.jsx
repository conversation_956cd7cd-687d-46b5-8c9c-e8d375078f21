'use client'
import ProfileSectionCard from './ProfileSectionCard';
import { Star } from 'lucide-react';
import ListInput from '../inputs/ListInput';

export default function ProfileSpecialisationSection({ profileId, value = [], onSubmit }) {
  return (
    <ProfileSectionCard title="Specialisation" icon={Star}>
      <ListInput
        label="Add Specialisation"
        items={value || []}
        placeholder="e.g., Generative AI"
        addLabel="Add"
        profileId={profileId}
        onSubmit={onSubmit}
        headerValue='specialisation'
      />
    </ProfileSectionCard>
  );
}
