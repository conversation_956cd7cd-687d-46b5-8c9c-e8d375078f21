import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function ProfileSectionCard({ title, icon: Icon, children, className = '' }) {
  return (
    <Card className={`border-0 shadow-lg bg-gradient-to-br from-card to-card/80 backdrop-blur-sm ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {Icon ? <Icon className="w-5 h-5 text-primary" /> : null}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}
