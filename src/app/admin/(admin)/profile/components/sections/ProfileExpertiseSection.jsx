'use client'
import ProfileSectionCard from './ProfileSectionCard';
import { TrendingUp } from 'lucide-react';
import ListInput from '../inputs/ListInput';

export default function ProfileExpertiseSection({ profileId, value = [], onSubmit }) {
  return (
    <ProfileSectionCard title="Expertise" icon={TrendingUp}>
      <ListInput
        label="Add Expertise"
        items={value || []}
        onChange={(v) => console.log(v)}
        placeholder="e.g., Web Performance"
        addLabel="Add"
        profileId={profileId}
        onSubmit={onSubmit}
        headerValue='expertise'
      />
    </ProfileSectionCard>
  );
}
