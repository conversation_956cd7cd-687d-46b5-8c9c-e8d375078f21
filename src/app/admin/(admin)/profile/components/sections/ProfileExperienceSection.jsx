'use client'
import { useState, useEffect } from 'react';
import ProfileSectionCard from './ProfileSectionCard';
import { Briefcase, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

export default function ProfileExperienceSection({ profileId, value = [], onChange, onSubmit, }) {
  const [list, setList] = useState(value || []);

  useEffect(() => {
    setList(value || []);
  }, [value]);

  const add = () => setList(prev => [...prev, { role: '', company: '', duration: '', description: '' }]);

  const update = (idx, key, val) => {
    const next = list.map((item, i) => i === idx ? { ...item, [key]: val } : item);
    setList(next);
    console.log(next);
    // onChange?.(next);
  };

  const remove = (idx) => {
    const next = list.filter((_, i) => i !== idx);
    setList(next);
    onChange?.(next);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await onSubmit(profileId, {
        experience: list
      });
      toast.success('Experience added.')
    } catch (error) {
      toast.error('Error updating Work Experience');
      console.log(error);
    }
  }

  return (
    <ProfileSectionCard title="Work Experience" icon={Briefcase}>
      <div className="space-y-4">
        {list?.map((exp, idx) => (
          <div key={idx} className="grid gap-3 p-3 border-2 rounded-lg bg-muted/50">
            <Input value={exp.role} onChange={(e) => update(idx, 'role', e.target.value)} placeholder="Role" />
            <Input value={exp.company} onChange={(e) => update(idx, 'company', e.target.value)} placeholder="Company" />
            <Input value={exp.duration} onChange={(e) => update(idx, 'duration', e.target.value)} placeholder="Duration" />
            <Textarea value={exp.description} onChange={(e) => update(idx, 'description', e.target.value)} placeholder="Description" />
            <div className="flex justify-end">
              <Button type="button" variant="ghost" className="text-destructive" onClick={() => remove(idx)}>Remove</Button>
            </div>
          </div>
        ))}
        <div className='flex items-center justify-between'>
          <Button
            type="button"
            onClick={add}
            variant={'outline'}
          >
            Add <Plus size={18} />
          </Button>
          {list !== value && <Button onClick={handleSubmit}>Save Changes</Button>}
        </div>
      </div>
    </ProfileSectionCard>
  );
}

