'use client';
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { Database, Download, Upload, RefreshCw, CheckCircle, AlertTriangle, HardDrive, FileText, Trash2, Calendar, Shield, Zap, Archive, CloudDownload, History, Settings, RotateCcw } from "lucide-react";
import pbclient from "@/lib/db";
import slugify from "@/utils/slugify";
import { fileSize } from "@/utils/fileSize";

export default function BackupPage() {
  // Fetch backup data from PocketBase
  const [backupHistory, setBackupHistory] = useState([]);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [restoreProgress, setRestoreProgress] = useState(0);

  const authAdmin = async () => {
    await pbclient.collection("_superusers").authWithPassword('<EMAIL>', 'TechBlogs@#0283093102');
  }

  const backupCreate = async () => {
    await authAdmin();
    await pbclient.backups.create(`pb_${slugify(new Date().toDateString())}_${slugify(new Date().toTimeString())}.zip`);
  }

  const backupList = async () => {
    await authAdmin();
    const backups = await pbclient.backups.getFullList();
    return backups
  }

  const backupDownload = async (file_name) => {
    await authAdmin();
    const token = await pbclient.files.getToken();
    const url = pbclient.backups.getDownloadUrl(token, file_name);
    console.log(url);
    return url
  }

  // Load backup history from PocketBase
  useEffect(() => {
    const fetchData = async () => {
      try {
        const backups = await backupList();
        console.log('Backups', backups);
        setBackupHistory(backups);
      } catch (error) {
        toast.info('Error getting backups')
        console.log(error);
      }
    }

    fetchData();
  }, []);

  // Handle backup creation
  const handleCreateBackup = async () => {
    setIsBackingUp(true);
    setBackupProgress(0);
    try {
      await backupCreate();
      // Simulate backup process with progress
      const steps = [
        { name: "Preparing backup...", duration: 500 },
        { name: "Backing up database...", duration: 1000 },
        { name: "Backing up posts...", duration: 1500 },
        { name: "Backing up media...", duration: 1000 },
        { name: "Compressing files...", duration: 800 },
        { name: "Finalizing backup...", duration: 200 }
      ];

      let progress = 0;
      for (const step of steps) {
        toast.info(step.name);
        await new Promise(resolve => setTimeout(resolve, step.duration));
        progress += 100 / steps.length;
        setBackupProgress(Math.round(progress));
      }
    } catch (error) {
      toast.error("Failed to create backup");
      console.error(error);
    } finally {
      setIsBackingUp(false);
      setBackupProgress(0);
    }
  };

  // Handle restore
  const handleRestore = async (file) => {
    if (!file) return;
    setIsRestoring(true);
    setRestoreProgress(0);

    try {
      authAdmin();
      await pbclient.backups.restore(file);
      // Simulate restore process with progress
      const steps = [
        { name: "Validating backup file...", duration: 500 },
        { name: "Restoring database...", duration: 1200 },
        { name: "Restoring posts...", duration: 1500 },
        { name: "Restoring settings...", duration: 800 },
        { name: "Rebuilding indexes...", duration: 1000 },
        { name: "Finalizing restore...", duration: 300 }
      ];

      let progress = 0;
      for (const step of steps) {
        toast.info(step.name);
        await new Promise(resolve => setTimeout(resolve, step.duration));
        progress += 100 / steps.length;
        setRestoreProgress(Math.round(progress));
      }

      toast.success("Data restored successfully!");
    } catch (error) {
      toast.error("Failed to restore data. Invalid backup file.");
      console.error(error);
    } finally {
      setIsRestoring(false);
      setRestoreProgress(0);
    }
  };

  // Handle delete backup
  const handleDeleteBackup = async (backupId) => {
    if (confirm("Are you sure you want to delete this backup?")) {
      try {
        await authAdmin();
        await pbclient.backups.delete(backupId);
        toast.success("Backup deleted successfully");
      } catch (error) {
        toast.error("Failed to delete backup");
        console.error(error);
      }
    }
  };

  // Show loading state
  if (!backupHistory) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <div className="h-8 w-64 bg-muted rounded loading-skeleton mb-2"></div>
            <div className="h-4 w-96 bg-muted rounded loading-skeleton"></div>
          </div>
          <div className="flex gap-3">
            <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
          </div>
        </div>

        <div className="space-y-6">
          <Card className="glass">
            <CardHeader>
              <div className="h-6 w-32 bg-muted rounded loading-skeleton"></div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-4 bg-muted rounded loading-skeleton"></div>
              <div className="h-3 bg-muted rounded loading-skeleton"></div>
              <div className="grid grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-16 bg-muted rounded loading-skeleton"></div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <Card key={i} className="glass">
                <CardHeader>
                  <div className="h-6 w-32 bg-muted rounded loading-skeleton"></div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="h-10 bg-muted rounded loading-skeleton"></div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-headline flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              <Database className="h-6 w-6" />
            </div>
            Backup & Restore
          </h1>
          <p className="text-muted-foreground">
            Manage your data backups and restore points for data protection.
          </p>
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleCreateBackup}
            disabled={isBackingUp || isRestoring}
            className="btn-modern"
          >
            {isBackingUp ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            {isBackingUp ? "Creating..." : "Create Backup"}
          </Button>
        </div>
      </div>

      {/* Progress Indicators */}
      {(isBackingUp || isRestoring) && (
        <Card className="glass border-primary/20">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {isBackingUp ? "Creating Backup" : "Restoring Data"}
                </span>
                <span className="text-sm text-muted-foreground">
                  {isBackingUp ? backupProgress : restoreProgress}%
                </span>
              </div>
              <Progress
                value={isBackingUp ? backupProgress : restoreProgress}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Backup History */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Backup History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {backupHistory?.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Archive className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No backups found</p>
                <p className="text-sm">Create your first backup to get started</p>
              </div>
            ) : (
              <div className="space-y-3">
                {backupHistory.map((backup, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 rounded-lg border border-border/50 hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <FileText className="h-4 w-4 text-primary" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{backup?.key}</span>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(backup?.modified).toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <HardDrive className="h-3 w-3" />
                            {fileSize(backup?.size / 1000)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8"
                        onClick={() => handleRestore(backup?.key)}
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={async () => {
                          try {
                            const url = await backupDownload(backup?.key);
                            if (!url) throw new Error("No file URL returned");

                            // Trigger browser download
                            const a = document.createElement("a");
                            a.href = url;
                            a.download = ""; // Let browser pick file name
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);

                            toast.success("Backup download started");
                          } catch (error) {
                            toast.error("Failed to download backup");
                            console.error(error);
                          }
                        }}
                        className="h-8"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteBackup(backup.key)}
                        className="h-8 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
