'use client'

import { useMemo, useState } from 'react'
import { useCollection } from '@/hooks/useCollection'
import { subDays, subMonths, isAfter, } from 'date-fns'
import {
} from 'recharts'
import DashboardHeader from './components/DashboardHeader'
import DashboardStats from './components/DashboardStats'
import DashboardMostViewed from './components/DashboardMostViewed'
import DashboardGrowthChart from './components/DashboardGrowthChart'
import DashboardQuickActions from './components/DashboardQuickActions'
import DashboardRecents from './components/DashboardRecents'

export default function DashboardPage() {
  // State for filters
  const [dateRange, setDateRange] = useState('30d')

  // Fetch live data from PocketBase collections
  const { data: blogs } = useCollection('blogs', { sort: '-created' })
  const { data: categories } = useCollection('categories')
  const { data: subscribers } = useCollection('newsletter', { sort: '-created' })

  // Helper function to filter data by date range
  const filterByDateRange = (data, dateField = 'created') => {
    if (!data) return []

    if (dateRange === 'all') return data

    const now = new Date()
    let startDate

    switch (dateRange) {
      case '7d':
        startDate = subDays(now, 7)
        break
      case '30d':
        startDate = subDays(now, 30)
        break
      case '3m':
        startDate = subMonths(now, 3)
        break
      case '6m':
        startDate = subMonths(now, 6)
        break
      case '1y':
        startDate = subMonths(now, 12)
        break
      default:
        return data
    }

    return data.filter(item => {
      const itemDate = new Date(item[dateField])
      return isAfter(itemDate, startDate)
    })
  }

  // Calculate statistics
  const stats = useMemo(() => {
    const allBlogs = blogs || []
    const allSubscribers = subscribers || []
    const filteredBlogs = filterByDateRange(allBlogs)
    const filteredSubscribers = filterByDateRange(allSubscribers)

    // Total viewers (sum of all blog views)
    const totalViewers = allBlogs.reduce((sum, blog) => sum + (blog.views || 0), 0)

    // Total subscribers
    const totalSubscribers = allSubscribers.filter(sub => sub.subscribed).length

    // Most popular category
    const categoryViews = {}
    allBlogs.forEach(blog => {
      const categoryId = blog.category || 'uncategorized'
      categoryViews[categoryId] = (categoryViews[categoryId] || 0) + (blog.views || 0)
    })
    const mostPopularCategoryId = Object.keys(categoryViews).reduce((a, b) =>
      categoryViews[a] > categoryViews[b] ? a : b, Object.keys(categoryViews)[0]
    )
    const mostPopularCategory = categories?.find(cat => cat.id === mostPopularCategoryId)?.title || 'Uncategorized'

    // Most popular article
    const mostPopularArticle = allBlogs.reduce((prev, current) =>
      (prev.views || 0) > (current.views || 0) ? prev : current, allBlogs[0] || {}
    )

    return {
      totalViewers,
      totalSubscribers,
      mostPopularCategory,
      mostPopularArticle: mostPopularArticle?.title || 'No articles yet',
      filteredBlogs,
      filteredSubscribers
    }
  }, [blogs, subscribers, categories, dateRange])

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header with Welcome and Filters */}
      <DashboardHeader dateRange={dateRange} setDateRange={setDateRange} />

      {/* Stats Cards */}
      <DashboardStats stats={stats} />

      {/* Most Viewed Articles */}
      <DashboardMostViewed blogs={blogs} categories={categories} />

      {/* Charts Grid */}
      <DashboardGrowthChart blogs={blogs} categories={categories} subscribers={subscribers} dateRange={dateRange} />


      {/* Additional Analytics Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Quick Actions */}
        <DashboardQuickActions />

        {/* Recent Activity */}
        <DashboardRecents stats={stats} />
      </div>
    </div>
  )
};
