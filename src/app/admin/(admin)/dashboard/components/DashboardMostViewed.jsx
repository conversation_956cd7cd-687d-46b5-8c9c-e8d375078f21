import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import pbclient from '@/lib/db';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { useMemo } from 'react';
import {
  TrendingUp,
  Eye,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function DashboardMostViewed({ blogs, categories }) {
  // Most viewed articles
  const mostViewedArticles = useMemo(() => {
    if (!blogs) return []
    return [...blogs]
      .sort((a, b) => (b.views || 0) - (a.views || 0))
      .slice(0, 4)
      .map(blog => {
        const category = categories?.find(cat => cat.id === blog.category)
        return {
          ...blog,
          categoryName: category?.title || 'Uncategorized'
        }
      })
  }, [blogs, categories]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Most Viewed Articles
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {mostViewedArticles.map((article, index) => (
            <Link key={index} href={`/admin/blogs/${article?.slug}`}>
              <Card className="relative overflow-hidden group hover:shadow-lg transition-shadow">
                <div className="absolute top-2 left-2 z-30 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                  #{index + 1}
                </div>

                {/* Image block */}
                <div className='h-50 w-full relative -mt-6'>
                  <LazyLoadingImage
                    src={pbclient.files.getURL(article, article?.thumbnail) || '/placeholder.png'}
                    alt={article?.thumbnail || 'thumbnail'}
                    className={'absolute inset-0 w-full h-full object-cover'}
                  />
                </div>

                <CardContent>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors">
                      {article.title}
                    </h3>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <Badge variant="secondary" className="text-xs">
                        {article.categoryName}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        <span>{(article.views || 0).toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {article.readTime || '5 min read'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
};
