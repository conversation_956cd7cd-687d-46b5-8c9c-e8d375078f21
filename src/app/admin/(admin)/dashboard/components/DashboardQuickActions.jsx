import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import {
  Users,
  FileText,
  Star,
  Filter,
} from 'lucide-react';

export default function DashboardQuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          <Link
            href="/admin/blogs"
            className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center group"
          >
            <FileText className="h-6 w-6 mx-auto mb-2 text-primary group-hover:scale-110 transition-transform" />
            <div className="text-sm font-medium">Manage Blogs</div>
          </Link>
          <Link
            href="/admin/newsletters"
            className="p-4 rounded-lg bg-green-500/10 hover:bg-green-500/20 transition-colors text-center group"
          >
            <Users className="h-6 w-6 mx-auto mb-2 text-green-500 group-hover:scale-110 transition-transform" />
            <div className="text-sm font-medium">Newsletters</div>
          </Link>
          <Link
            href="/admin/categories"
            className="p-4 rounded-lg bg-blue-500/10 hover:bg-blue-500/20 transition-colors text-center group"
          >
            <Star className="h-6 w-6 mx-auto mb-2 text-blue-500 group-hover:scale-110 transition-transform" />
            <div className="text-sm font-medium">Categories</div>
          </Link>
          <Link
            href="/admin/tags"
            className="p-4 rounded-lg bg-purple-500/10 hover:bg-purple-500/20 transition-colors text-center group"
          >
            <Filter className="h-6 w-6 mx-auto mb-2 text-purple-500 group-hover:scale-110 transition-transform" />
            <div className="text-sm font-medium">Tags</div>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
};
