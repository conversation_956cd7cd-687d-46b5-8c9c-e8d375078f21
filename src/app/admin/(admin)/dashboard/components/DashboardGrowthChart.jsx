import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { AreaChart, Area, BarChart, Bar, ResponsiveContainer, XAxis, YAxis, LineChart, Line, CartesianGrid, } from 'recharts';
import { Users, Eye, TrendingUp, } from 'lucide-react'
import { useMemo } from 'react';
import { format, subDays, } from 'date-fns'
import { colorScheme } from '@/constants/colors';

export default function DashboardGrowthChart({ blogs, categories, subscribers, dateRange }) {

  // Chart data for subscriber growth
  const subscriberGrowthData = useMemo(() => {
    if (!subscribers) return []

    const data = []
    const now = new Date()
    const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90

    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(now, i)
      const daySubscribers = subscribers.filter(sub => {
        const subDate = new Date(sub.created)
        return subDate.toDateString() === date.toDateString() && sub.subscribed
      }).length

      data.push({
        date: format(date, 'MMM dd'),
        subscribers: daySubscribers,
        cumulative: subscribers.filter(sub => {
          const subDate = new Date(sub.created)
          return subDate <= date && sub.subscribed
        }).length
      })
    }

    return data
  }, [subscribers, dateRange])


  // Chart configuration
  const chartConfig = {
    subscribers: {
      label: "Subscribers",
      color: colorScheme.primary,
    },
    cumulative: {
      label: "Total Subscribers",
      color: colorScheme.primary,
    },
    views: {
      label: "Views",
      color: colorScheme.primary,
    },
    blogs: {
      label: "Articles",
      color: colorScheme.primary,
    }
  }
  // Chart data for category-wise blog views
  const categoryViewsData = useMemo(() => {
    if (!blogs || !categories) return []

    const categoryData = categories.map(category => {
      const categoryBlogs = blogs.filter(blog => blog.category === category.id)
      const totalViews = categoryBlogs.reduce((sum, blog) => sum + (blog.views || 0), 0)
      const blogCount = categoryBlogs.length

      return {
        name: category.title,
        views: totalViews,
        blogs: blogCount,
        avgViews: blogCount > 0 ? Math.round(totalViews / blogCount) : 0
      }
    })

    return categoryData.sort((a, b) => b.views - a.views)
  }, [blogs, categories]);

  return (
    <>
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        {/* Subscriber Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Subscriber Growth Over Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={subscriberGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area
                    type="monotone"
                    dataKey="cumulative"
                    stroke={colorScheme.primary}
                    fill={colorScheme.primary_30}
                    fillOpacity={0.2}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Category Views Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Blog Views by Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={categoryViewsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar
                    dataKey="views"
                    stroke={colorScheme.primary}
                    fill={colorScheme.primary_30}
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Newsletter Subscriber Growth Chart - Full Width */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Newsletter Subscriber Growth
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={subscriberGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="subscribers"
                  stroke={colorScheme.primary_30}
                  strokeWidth={3}
                  dot={{ fill: `${colorScheme.primary}`, strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: `${colorScheme.primary}`, strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="cumulative"
                  stroke={colorScheme.primary_30}
                  strokeDasharray="5 5"
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>
    </>
  )
};
