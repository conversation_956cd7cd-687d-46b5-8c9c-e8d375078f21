import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Users,
  Eye,
  FileText,
  Star,
} from 'lucide-react'

// Enhanced StatCard with trend indicators
function StatCard({ title, value, icon: Icon, color = "text-primary" }) {
  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {Icon && <Icon className={`h-4 w-4 ${color}`} />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
      </CardContent>
    </Card>
  )
}

export default function DashboardStats({ stats }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Views"
        value={stats.totalViewers.toLocaleString()}
        icon={Eye}
        color="text-blue-500"
      />
      <StatCard
        title="Total Subscribers"
        value={stats.totalSubscribers.toLocaleString()}
        icon={Users}
        color="text-green-500"
      />
      <StatCard
        title="Most Popular Category"
        value={stats.mostPopularCategory}
        icon={Star}
        color="text-yellow-500"
      />
      <StatCard
        title="Most Popular Article"
        value={stats.mostPopularArticle.length > 20 ?
          stats.mostPopularArticle.substring(0, 20) + '...' :
          stats.mostPopularArticle
        }
        icon={FileText}
        color="text-purple-500"
      />
    </div>
  )
};
