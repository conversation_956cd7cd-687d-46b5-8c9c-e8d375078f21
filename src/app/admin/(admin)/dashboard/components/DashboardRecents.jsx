import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

export default function DashboardRecents({ stats }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stats.filteredBlogs.slice(0, 5).map((blog) => (
            <div key={blog.id} className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{blog.title}</p>
                <p className="text-xs text-muted-foreground">
                  {format(new Date(blog.created), 'MMM dd, yyyy')}
                </p>
              </div>
              <Badge variant="outline" className="text-xs">
                {blog.views || 0} views
              </Badge>
            </div>
          ))}
          {stats.filteredBlogs.length === 0 && (
            <div className="text-sm text-muted-foreground text-center py-4">
              No recent activity
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
};
