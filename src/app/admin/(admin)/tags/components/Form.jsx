import { useState } from "react";
import { Upload, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";

export default function Form() {
  const { createItem, mutation } = useCollection('tags');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
  });

  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value, files, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'file' ? files?.[0] : value,
    }));
  };

  const handleReset = () => {
    setFormData({
      title: '',
      description: '',
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = {
        title: formData.title || '',
        description: formData.description || ''
      };

      const newTag = await createItem(payload);
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.CREATE,
        module: `Tags`,
        details: newTag
      });
      toast.success('Added the new tag');
    } catch (error) {
      console.log(error)
      toast.error(error.message);
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      className='max-w-2xl'
    >
      <DialogTrigger asChild>
        <Button
          className='rounded-md text-sm'
        >
          <p>Add Tag</p>
          <Plus className='w-5 h-5' />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Tag</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter tag title"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Short description"
              className="bg-accent"
            />
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
            >
              <p>Add Tag</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
