import { DataTable } from '@/components/ui/data-table';
import { useCollection } from '@/hooks/useCollection'
import React from 'react'
import Form from './Form';
import EditForm from './EditForm';
import MobileDataTable from '@/components/ui/mobile-data-table';
import { useIsMobile } from '@/hooks/use-mobile';
import DetailsActions from '@/components/actions-buttons/DetailsActions';

export default function TagsLayout() {
  const { data, deleteItem } = useCollection('tags');

  const columns = [
    {
      id: 'id',
      accessorKey: 'id',
      header: 'Tag ID',
      filterable: true,
      cell: ({ row }) => <div>{row.original.id}</div>,
    },
    {
      id: 'title',
      accessorKey: 'title',
      header: 'Title',
      filterable: true,
      cell: ({ row }) => <div>{row.original?.title}</div>,
    },
    {
      id: 'description',
      accessorKey: 'description',
      header: 'Description',
      filterable: true,
      cell: ({ row }) => <div>{row.original.description}</div>,
    },
    {
      id: 'actions',
      accessorKey: 'actions',
      header: 'Actions',
      filterable: false,
      cell: ({ row }) => (
        <DetailsActions
          row={row}
          EditForm={EditForm}
          deleteItem={deleteItem}
          showEye={false}
        />
      ),
    }
  ];

  return (
    <>
      {
        useIsMobile() ? (
          <div className="border-2 bg-background md:p-4 rounded-xl mt-8">
            <h1 className="text-xl font-semibold p-4">Tags</h1>
            <div className="flex justify-end p-4">
              <Form />
            </div>
            <MobileDataTable
              columns={columns}
              data={data}
            />
          </div>
        ) : (
          <div className="border-2 bg-background dark:bg-accent md:p-4 rounded-xl mt-8">
            <div className="flex items-center justify-between gap-4">
              <h1 className="text-lg font-semibold">Tags</h1>
              <Form />
            </div>

            <DataTable
              columns={columns}
              data={data}
            />
          </div>
        )
      }
    </>
  )
};
