import { But<PERSON> } from "@/components/ui/button";
import { XCircle } from "lucide-react";
import { toast } from "sonner";

export default function InquiryDelete({
  deleteItem,
  inquiry
}) {
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this inquiry?")) {
      try {
        await deleteItem(id);
        toast.success("Inquiry deleted successfully");
      } catch (error) {
        toast.error("Failed to delete inquiry");
        console.error(error);
      }
    }
  };

  return (
    <Button
      size="sm"
      variant="ghost"
      onClick={() => handleDelete(inquiry.id)}
      className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
      title="Delete inquiry"
    >
      <XCircle className="h-3 w-3" />
    </Button>
  )
};
