import { Card, CardContent, CardHeader } from "@/components/ui/card";

export default function InquiresLoadingScreen() {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div className="h-8 w-64 bg-muted rounded loading-skeleton mb-2"></div>
          <div className="h-4 w-96 bg-muted rounded loading-skeleton"></div>
        </div>
        <div className="flex gap-3">
          <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
          <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
        </div>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i} className="glass">
            <CardHeader className="pb-2">
              <div className="h-4 w-20 bg-muted rounded loading-skeleton"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-12 bg-muted rounded loading-skeleton"></div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-6">
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i} className="glass">
            <CardHeader className="pb-4">
              <div className="h-6 w-24 bg-muted rounded loading-skeleton"></div>
            </CardHeader>
            <CardContent className="space-y-3">
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className="h-20 bg-muted rounded loading-skeleton"></div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
};
