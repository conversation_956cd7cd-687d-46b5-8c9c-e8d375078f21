import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import Form from "./Form";

export default function InquiryHeader({
  query,
  setQuery,
  formData,
  setFormData,
  editingInquiry,
  setEditingInquiry,
  isDialogOpen,
  setIsDialogOpen,
  services,
  STATUSES,
}) {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 className="text-headline">Inquiries Management</h1>
        <p className="text-muted-foreground">
          Manage customer inquiries and track their progress through the sales pipeline.
        </p>
      </div>

      <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
        <div className="relative flex-1 sm:flex-none">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search inquiries..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10 w-full sm:w-[280px] lg:w-[320px]"
          />
        </div>

        <Form
          formData={formData}
          setFormData={setFormData}
          editingInquiry={editingInquiry}
          setEditingInquiry={setEditingInquiry}
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
          services={services}
          STATUSES={STATUSES}
        />
      </div>
    </div>
  )
};
