import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";

export default function EditForm({
  setFormData,
  setEditingInquiry,
  setIsDialogOpen,
  inquiry
}) {
  // Handle edit
  const handleEdit = (inquiry) => {
    setEditingInquiry(inquiry);
    setFormData({
      name: inquiry.name || "",
      email: inquiry.email || "",
      companyName: inquiry.companyName || "",
      service: inquiry.service || "",
      budget: inquiry.budget || "",
      timeline: inquiry.timeline || "",
      description: inquiry.description || "",
      status: inquiry.status || "pending"
    });
    setIsDialogOpen(true);
  };
  return (
    <Button
      size="sm"
      variant="ghost"
      onClick={() => handleEdit(inquiry)}
      className="h-7 w-7 p-0 hover:bg-accent"
      title="Edit inquiry"
    >
      <FileText className="h-3 w-3" />
    </Button>
  )
};
