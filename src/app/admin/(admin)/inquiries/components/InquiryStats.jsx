import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Clock, FileText, TrendingUp } from "lucide-react";

export default function InquiryStats({ inquiries }) {
  // Calculate statistics
  const stats = useMemo(() => {
    if (!inquiries) return { total: 0, pending: 0, inProgress: 0, deals: 0, completed: 0 };

    return {
      total: inquiries.length,
      pending: inquiries.filter(i => i.status === 'pending').length,
      inProgress: inquiries.filter(i => i.status === 'in_progress').length,
      deals: inquiries.filter(i => i.status === 'deal').length,
      completed: inquiries.filter(i => i.status === 'completed').length,
    };
  }, [inquiries]);

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Total Inquiries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pending
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            In Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.deals}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Completed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-emerald-600">{stats.completed}</div>
        </CardContent>
      </Card>
    </div>
  )
};
