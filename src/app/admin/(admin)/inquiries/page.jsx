'use client';
import React, { useState } from "react";
import { useCollection } from "@/hooks/useCollection";
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import InquiresLoadingScreen from "./components/InquiresLoadingScreen";
import InquiryHeader from "./components/InquiryHeader";
import InquiryStats from "./components/InquiryStats";
import InquiryKanbanBoard from "./components/InquiryKanbanBoard";

export default function InquiriesPage() {
  const STATUSES = [
    { value: "pending", label: "Pending", icon: Clock, color: "bg-yellow-500" },
    { value: "in_progress", label: "In Progress", icon: AlertCircle, color: "bg-blue-500" },
    { value: "deal", label: "Deal", icon: CheckCircle, color: "bg-green-500" },
    { value: "completed", label: "Completed", icon: CheckCircle, color: "bg-emerald-500" },
    { value: "no_deal", label: "No Deal", icon: XCircle, color: "bg-red-500" },
  ];

  // Fetch inquiries and services from PocketBase
  const { data: inquiries } = useCollection('inquiries', {
    expand: 'service',
    sort: '-created'
  });
  const { data: services } = useCollection('services');

  const [query, setQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingInquiry, setEditingInquiry] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    companyName: "",
    service: "",
    budget: "",
    timeline: "",
    description: "",
    status: "pending"
  });

  // Show loading state
  if (!inquiries || !services) {
    return (
      <InquiresLoadingScreen />
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <InquiryHeader
        query={query}
        setQuery={setQuery}
        formData={formData}
        setFormData={setFormData}
        editingInquiry={editingInquiry}
        setEditingInquiry={setEditingInquiry}
        isDialogOpen={isDialogOpen}
        setIsDialogOpen={setIsDialogOpen}
        services={services}
        STATUSES={STATUSES}
      />

      {/* Statistics Cards */}
      <InquiryStats inquiries={inquiries} />

      {/* Kanban Board */}
      <InquiryKanbanBoard
        STATUSES={STATUSES}
        inquiries={inquiries}
        query={query}
        setFormData={setFormData}
        setEditingInquiry={setEditingInquiry}
        setIsDialogOpen={setIsDialogOpen}
      />
    </div>
  );
};
