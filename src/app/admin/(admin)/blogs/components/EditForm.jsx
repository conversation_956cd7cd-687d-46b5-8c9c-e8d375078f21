import { useState } from "react";
import { Upload, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";
import { DynamicMultiDatalist } from "@/components/ui/data-multi-list";
import { DynamicDatalist } from "@/components/ui/data-list";

export default function EditForm({
  info = { id: '', title: '', description: '', slug: '', readTime: '', expand: { tags: [], category: null } }
}) {
  const { updateItem, mutation } = useCollection('blogs');
  const { data: listTags } = useCollection('tags');
  const { data: listCategories } = useCollection('categories');

  const [tags, setTags] = useState(info?.expand?.tags || []);
  const [formData, setFormData] = useState({
    id: info?.id,
    title: info?.title || '',
    slug: info?.slug || '',
    description: info?.description || '',
    readTime: info?.readTime || '',
    categoryId: info?.expand?.category?.id || info?.category || '',
    content: null, // optional replace
    assests: [], // optional add more
  });
  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;

    if (type === 'file') {
      if (name === 'assests') {
        setFormData((prev) => ({ ...prev, assests: Array.from(files || []) }));
      } else {
        setFormData((prev) => ({ ...prev, [name]: files?.[0] || null }));
      }
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleReset = () => {
    setFormData({
      id: info?.id,
      title: '',
      slug: '',
      description: '',
      readTime: '',
      categoryId: '',
      content: null,
      assests: [],
    });
    setTags([]);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = new FormData();
      payload.append('title', formData.title || '');
      payload.append('slug', formData.slug || '');
      payload.append('description', formData.description || '');
      payload.append('readTime', formData.readTime || '');
      if (formData.categoryId) payload.append('category', formData.categoryId);
      payload.append('tags', JSON.stringify((tags || []).map(t => t?.id || t).filter(Boolean)));
      if (formData.content) payload.append('content', formData.content);
      if (Array.isArray(formData.assests)) {
        for (const file of formData.assests) payload.append('assests', file);
      }

      const updatedBlog = await updateItem(formData.id, payload);
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.EDIT,
        module: `Blogs`,
        details: updatedBlog
      });
      toast.success('Updated the blog');
    } catch (error) {
      console.log(error)
      toast.error(error.message || 'Failed to update blog');
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} className='bg-accent w-full cursor-pointer max-w-2xl'>
      <DialogTrigger asChild>
        <button className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'>
          <p>Edit Details</p>
          <Pencil size={18} className="cursor-pointer" />
        </button>
      </DialogTrigger>
      <DialogContent className={'bg-accent max-w-2xl'}>
        <DialogHeader>
          <DialogTitle>Update Blog</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input type="text" name="title" value={formData.title} onChange={handleChange} placeholder="e.g., Artificial Intelligence" className="bg-accent" />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Slug</Label>
            <Input type="text" name="slug" value={formData.slug} onChange={handleChange} placeholder="e.g., artificial-intelligence" className="bg-accent" />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea name="description" value={formData.description} onChange={handleChange} placeholder="Short description" className="bg-accent" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col gap-2">
              <Label>Read Time</Label>
              <Input type="text" name="readTime" value={formData.readTime} onChange={handleChange} placeholder="e.g., 6 min" className="bg-accent" />
            </div>
            <div className="flex flex-col gap-2">
              <Label>Category</Label>
              <DynamicDatalist label="Categories" items={listCategories || []} getLabel={(c) => c?.title} placeholder="Select category..." onSelect={(item) => setFormData((p) => ({ ...p, categoryId: item?.id }))} />
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <Label>Tags</Label>
            <DynamicMultiDatalist items={listTags || []} existing={tags || []} label="Tags" getLabel={(c) => c?.title} placeholder="Select tag(s)..." onChange={(selected) => setTags(selected)} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Replace Content (.md)</Label>
            <Input type="file" accept=".md,.markdown,.mdx,text/markdown,text/plain" name="content" onChange={handleChange} className="bg-accent" />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Add/Replace Assets (images)</Label>
            <Input type="file" accept="image/*" name="assests" multiple onChange={handleChange} className="bg-accent" />
          </div>

          <div className="mt-6">
            <Button onClick={handleSubmit}>
              <p>Update Blog</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
};
