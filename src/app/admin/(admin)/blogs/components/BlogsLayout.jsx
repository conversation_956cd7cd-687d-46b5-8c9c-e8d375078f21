import { DataTable } from '@/components/ui/data-table';
import { useCollection } from '@/hooks/useCollection'
import React from 'react'
import Form from './Form';
import EditForm from './EditForm';
import MobileDataTable from '@/components/ui/mobile-data-table';
import { useIsMobile } from '@/hooks/use-mobile';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import { Badge } from '@/components/ui/badge';
import pbclient from '@/lib/db';
import BlogsActions from '@/components/actions-buttons/BlogsActions';

export default function BlogsLayout() {
  const { data, deleteItem } = useCollection('blogs', {
    expand: 'category,tags,author',
  });

  const columns = [
    {
      id: 'id',
      accessorKey: 'id',
      header: 'Blog ID',
      filterable: true,
      cell: ({ row }) => <div className='w-[50px] whitespace-pre-line truncate'>{row.original.id}</div>,
    },
    {
      id: 'title',
      accessorKey: 'title',
      header: 'Title',
      filterable: true,
      cell: ({ row }) => <div className='w-[100px] whitespace-pre-line'>{row.original?.title}</div>,
    },
    {
      id: 'description',
      accessorKey: 'description',
      header: 'Description',
      filterable: true,
      cell: ({ row }) => <div className='w-[100px] whitespace-pre-line'>{row.original.description}</div>,
    },
    {
      id: 'slug',
      accessorKey: 'slug',
      header: 'Slug',
      filterable: true,
      cell: ({ row }) => <div className='w-[100px] whitespace-pre-line'>{row.original.slug}</div>,
    },
    {
      id: 'category',
      accessorKey: 'category',
      header: 'Category',
      filterable: true,
      cell: ({ row }) => {
        return (
          <div className={`grid gap-4 p-2`}>
            <Badge variant={'outline'}>
              {row.original?.expand?.category?.title}
            </Badge>
          </div>
        )
      }
    },
    {
      id: 'tags',
      accessorKey: 'tags',
      header: 'Tags',
      filterable: true,
      cell: ({ row }) => {
        return (
          <div className='p-2'>
            <div className={`grid gap-2 p-2`}>
              {row.original?.expand?.tags
                ?.slice(0, 4)
                ?.map((tag, index) => (
                  <Badge variant={'outline'} key={index} className={'w-full'}>
                    {tag?.title}
                  </Badge>
                ))}
            </div>
            {row.original?.expand?.tags?.length > 4 && (
              <Badge variant={'outline-primary'} className={'float-right px-3'}> + {row.original?.expand?.tags?.length - 4} more </Badge>
            )}
          </div>
        )
      }
    },
    {
      id: 'author',
      accessorKey: 'expand?.author?.name',
      header: 'Author',
      filterable: true,
      cell: ({ row }) => <div className='w-[100px] whitespace-pre-line'>{row.original?.expand?.author?.name}</div>,
    },
    {
      id: 'stats',
      accessorKey: 'stats',
      header: 'Stats',
      filterable: false,
      cell: ({ row }) => (
        <div className='w-[100px] grid text-xs'>
          <p>Time: {row.original?.readTime} </p>
          <p>Views: {row.original?.views?.toLocaleString()}</p>
        </div>
      ),
    },
    {
      id: 'assests',
      accessorKey: 'assests',
      header: 'Assets',
      filterable: false,
      cell: ({ row }) => {

        const assests = row.original?.assests;
        if (!assests) return null;
        return (
          <div className='w-[100px] grid grid-cols-2'>
            {
              assests.map((asset, index) => {
                const url = pbclient.files.getURL(row.original, asset);
                return (
                  <LazyLoadingImage
                    key={index}
                    src={url}
                    alt={row.original?.title || `asset ${index}`}
                    className="w-10 h-10 rounded-md object-cover"
                  />
                )
              })
            }
          </div>
        )
      }
    },
    {
      id: 'actions',
      accessorKey: 'actions',
      header: 'Actions',
      filterable: false,
      cell: ({ row }) => (
        <BlogsActions
          row={row}
          EditForm={EditForm}
          deleteItem={deleteItem}
          showEye={false}
        />
      ),
    }
  ];

  return (
    <>
      {
        useIsMobile() ? (
          <div className="border-2 bg-background md:p-4 rounded-xl mt-8">
            <h1 className="text-xl font-semibold p-4">Blogs</h1>
            <div className="flex justify-end p-4">
              <Form />
            </div>
            <MobileDataTable
              columns={columns}
              data={data}
            />
          </div>
        ) : (
          <div className="border-2 bg-background dark:bg-accent md:p-4 rounded-xl mt-8">
            <div className="flex items-center justify-between gap-4">
              <h1 className="text-lg font-semibold">Blogs</h1>
              <Form />
            </div>

            <DataTable
              columns={columns}
              data={data}
            />
          </div>
        )
      }
    </>
  )
};
