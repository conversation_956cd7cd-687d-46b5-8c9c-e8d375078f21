'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import '../sample/typo.css';
import { markdownAsHtml2 } from '@/utils/markdownConverter';
import OnThisPage from '@/components/blocks/on-this-page';
import pbclient from '@/lib/db';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AdminBlogPreviewPage() {
  const { slug } = useParams();
  const [blog, setBlog] = useState({
    title: '',
    description: '',
    author: { firstName: '', lastName: '' },
    thumbnail: '',
    publishedAt: '',
    content: '',
    rawContent: '',
    tags: [],
    slug: ''
  });

  useEffect(() => {
    if (!slug) return;
    const run = async () => {
      try {
        // Fetch blog by slug (requires user to be authenticated in admin)
        const rec = await pbclient.collection('blogs').getFirstListItem(`slug="${slug}"`, {
          expand: 'author,category,tags',
        });

        // Build file URL for the markdown content
        let html = '';
        let raw = '';
        if (rec?.content) {
          const contentUrl = pbclient.files.getURL(rec, rec.content);
          const parsed = await markdownAsHtml2({ url: contentUrl, title: rec.title });
          html = parsed?.html || '';
          raw = parsed?.raw || '';
        }

        const name = rec?.expand?.author?.name || '';
        const [firstName, ...rest] = name?.split?.(' ') || [''];
        const lastName = rest?.join(' ') || '';
        const thumbnail = pbclient.files.getURL(rec, rec?.thumbnail)

        setBlog({
          title: rec?.title || '',
          description: rec?.description || '',
          author: { firstName, lastName },
          publishedAt: new Date(rec?.created || Date.now()).toLocaleDateString(),
          thumbnail: thumbnail,
          content: html,
          rawContent: raw,
          tags: (rec?.expand?.tags || []).map((t) => t?.title || t),
          slug: rec?.slug || '',
        });
      } catch (err) {
        console.error('Failed to load blog preview:', err);
      }
    };
    run();
  }, [slug]);

  return (
    <main className='flex gap-8 items-start'>
      <div className="prose dark:prose-invert max-w-3xl mx-auto p-4">
        <LazyLoadingImage
          src={blog?.thumbnail || '/placeholder.png'}
          alt={'thumbnail'}
          className={'min-w-xl'}
        />
        <h1 className="text-4xl font-bold mb-4">{blog.title}</h1>
        <p className="text-base mb-2 border-l-4 border-gray-500 pl-4 italic">&quot;{blog.description}&quot;</p>
        <div className="flex gap-2">
          <p className="text-sm text-gray-500 mb-4 italic">By {blog?.author?.firstName || ''} {blog?.author?.lastName || ''}</p>
          <p className="text-sm text-gray-500 mb-4">{blog?.publishedAt}</p>
        </div>

        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="w-full grid grid-cols-2 md:w-fit">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="markdown">Markdown</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="pt-4">
            <div dangerouslySetInnerHTML={{ __html: blog.content }} />
          </TabsContent>

          <TabsContent value="markdown" className="pt-4">
            <pre className="p-4 rounded-md border bg-muted overflow-auto max-h-[60vh]"><code>{blog.rawContent}</code></pre>
          </TabsContent>
        </Tabs>
      </div>
      <OnThisPage htmlContent={blog.content} tags={blog.tags} sharePath={`/blogs/${blog.slug || ''}`} />
    </main>
  );
}

