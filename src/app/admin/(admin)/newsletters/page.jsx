'use client';

import React, { useState } from "react";
import { useCollection } from "@/hooks/useCollection";
import {
  UserCheck,
  UserX,
  Clock,
  UserPlus,
} from "lucide-react";
import NewsletterLoading from "./components/NewsletterLoading";
import NewsletterHeader from "./components/NewsletterHeader";
import NewsletterStats from "./components/NewsletterStats";
import NewsletterKanbanBoard from "./components/NewsletterKanbanBoard";

export default function NewslettersPage() {
  const STATUSES = [
    {
      value: "New Subscribers",
      label: "New Subscribers",
      icon: UserPlus,
      color: "bg-blue-500",
      filter: (subscriber) => subscriber.subscribed && isNewSubscriber(subscriber)
    },
    {
      value: "Active Subscribers",
      label: "Active Subscribers",
      icon: UserCheck,
      color: "bg-green-500",
      filter: (subscriber) => subscriber.subscribed && !isNewSubscriber(subscriber)
    },
    {
      value: "Pending Verification",
      label: "Pending Verification",
      icon: Clock,
      color: "bg-yellow-500",
      filter: (subscriber) => !subscriber.subscribed && isNewSubscriber(subscriber)
    },
    {
      value: "Unsubscribed",
      label: "Unsubscribed",
      icon: UserX,
      color: "bg-red-500",
      filter: (subscriber) => !subscriber.subscribed && !isNewSubscriber(subscriber)
    },
  ];

  // Helper function to determine if subscriber is new (within last 7 days)
  function isNewSubscriber(subscriber) {
    const createdDate = new Date(subscriber.created);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return createdDate > sevenDaysAgo;
  }

  // Fetch newsletter subscribers from PocketBase
  const { data: subscribers, createItem, updateItem, deleteItem } = useCollection('newsletter', {
    sort: '-created'
  });

  const [query, setQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSubscriber, setEditingSubscriber] = useState(null);
  const [formData, setFormData] = useState({
    email: "",
    subscribed: true
  });

  // Show loading state
  if (!subscribers) {
    return (
      <NewsletterLoading />
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <NewsletterHeader
        query={query}
        setQuery={setQuery}
        isDialogOpen={isDialogOpen}
        createItem={createItem}
        updateItem={updateItem}
        setIsDialogOpen={setIsDialogOpen}
        editingSubscriber={editingSubscriber}
        subscribers={subscribers}
        setEditingSubscriber={setEditingSubscriber}
        formData={formData}
        setFormData={setFormData}
      />

      {/* Statistics Cards */}
      <NewsletterStats subscribers={subscribers} isNewSubscriber={isNewSubscriber} />

      {/* Kanban Board */}
      <NewsletterKanbanBoard
        STATUSES={STATUSES}
        isNewSubscriber={isNewSubscriber}
        subscribers={subscribers}
        query={query}
        updateItem={updateItem}
        deleteItem={deleteItem}
        setEditingSubscriber={setEditingSubscriber}
        setFormData={setFormData}
        setIsDialogOpen={setIsDialogOpen}
      />
    </div>
  );
}
