import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Plus } from "lucide-react";
import { toast } from "sonner";

export default function NewsletterForm({
  isDialogOpen,
  createItem,
  updateItem,
  setIsDialogOpen,
  editingSubscriber,
  setEditingSubscriber,
  formData,
  setFormData,
}) {

  // Reset form
  const resetForm = () => {
    setFormData({
      email: "",
      subscribed: true
    });
    setEditingSubscriber(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      if (editingSubscriber) {
        await updateItem(editingSubscriber.id, formData);
        toast.success("Subscriber updated successfully");
      } else {
        // Check for duplicate email when creating new subscriber
        const existingSubscriber = subscribers?.find(s => s.email === formData.email);
        if (existingSubscriber) {
          toast.error("A subscriber with this email already exists");
          return;
        }

        await createItem(formData);
        toast.success("Subscriber added successfully");
      }
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to save subscriber");
      console.error(error);
    }
  };
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button onClick={resetForm} className="btn-modern">
          <Plus className="h-4 w-4" />
          Add Subscriber
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto mx-4 sm:mx-auto">
        <DialogHeader>
          <DialogTitle>
            {editingSubscriber ? "Edit Subscriber" : "Add New Subscriber"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
              required
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="subscribed">Subscription Status</Label>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {formData.subscribed ? "Subscribed" : "Unsubscribed"}
              </span>
              <Switch
                id="subscribed"
                checked={formData.subscribed}
                onCheckedChange={(checked) => setFormData({ ...formData, subscribed: checked })}
              />
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {editingSubscriber ? "Update" : "Add"} Subscriber
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
};
