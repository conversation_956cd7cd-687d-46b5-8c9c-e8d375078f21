import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Download, Search, UserX } from "lucide-react";
import NewsletterForm from "./NewsletterForm";

export default function NewsletterHeader({
  query,
  setQuery,
  isDialogOpen,
  createItem,
  updateItem,
  setIsDialogOpen,
  editingSubscriber,
  subscribers,
  setEditingSubscriber,
  formData,
  setFormData,
}) {
  // Handle export subscribers
  const handleExportSubscribers = () => {
    if (!subscribers || subscribers.length === 0) {
      toast.info("No subscribers to export");
      return;
    }

    const activeSubscribers = subscribers.filter(s => s.subscribed);
    if (activeSubscribers.length === 0) {
      toast.info("No active subscribers to export");
      return;
    }

    const csvContent = [
      "Email,Subscribed,Created Date",
      ...activeSubscribers.map(s =>
        `${s.email},${s.subscribed ? 'Yes' : 'No'},${new Date(s.created).toLocaleDateString()}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast.success(`Exported ${activeSubscribers.length} active subscribers`);
  };

  // Handle bulk unsubscribe for unsubscribed users
  const handleBulkCleanup = async () => {
    const unsubscribedUsers = subscribers?.filter(s => !s.subscribed) || [];
    if (unsubscribedUsers.length === 0) {
      toast.info("No unsubscribed users to remove");
      return;
    }

    if (confirm(`Are you sure you want to delete ${unsubscribedUsers.length} unsubscribed users? This action cannot be undone.`)) {
      try {
        const deletePromises = unsubscribedUsers.map(user => deleteItem(user.id));
        await Promise.all(deletePromises);
        toast.success(`Successfully removed ${unsubscribedUsers.length} unsubscribed users`);
      } catch (error) {
        toast.error("Failed to remove unsubscribed users");
        console.error(error);
      }
    }
  };

  return (
    <>
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-headline">Newsletter Management</h1>
          <p className="text-muted-foreground">
            Manage newsletter subscribers and track subscription status across your mailing list.
          </p>
        </div>
      </div>


      <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
        <div className="relative flex-1 sm:flex-none">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search email addresses..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10 w-full sm:w-[280px] lg:w-[320px]"
          />
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleExportSubscribers}
            className="btn-modern"
            disabled={!subscribers?.some(s => s.subscribed)}
          >
            <Download className="h-4 w-4" />
            Export CSV
          </Button>

          <Button
            variant="outline"
            onClick={handleBulkCleanup}
            className="btn-modern"
            disabled={!subscribers?.some(s => !s.subscribed)}
          >
            <UserX className="h-4 w-4" />
            Cleanup
          </Button>

          <NewsletterForm
            isDialogOpen={isDialogOpen}
            createItem={createItem}
            updateItem={updateItem}
            setIsDialogOpen={setIsDialogOpen}
            editingSubscriber={editingSubscriber}
            setEditingSubscriber={setEditingSubscriber}
            formData={formData}
            setFormData={setFormData}
          />

        </div>
      </div>
    </>
  )
};
