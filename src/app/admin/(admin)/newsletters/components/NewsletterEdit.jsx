import { But<PERSON> } from "@/components/ui/button";
import { MailOpen } from "lucide-react";

export default function NewsletterEdit({ subscriber, setEditingSubscriber, setFormData, setIsDialogOpen }) {
  // Handle edit
  const handleEdit = (subscriber) => {
    setEditingSubscriber(subscriber);
    setFormData({
      email: subscriber.email || "",
      subscribed: subscriber.subscribed || false
    });
    setIsDialogOpen(true);
  };

  return (
    <Button
      size="sm"
      variant="ghost"
      onClick={() => handleEdit(subscriber)}
      className="h-7 w-7 p-0 hover:bg-accent"
      title="Edit subscriber"
    >
      <MailOpen className="h-3 w-3" />
    </Button>
  )
};
