import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckCircle, Mail, XCircle } from "lucide-react";
import NewsletterEdit from "./NewsletterEdit";
import NewsletterDelete from "./NewsletterDelete";
import { useMemo } from "react";
import { toast } from "sonner";

export default function NewsletterKanbanBoard({
  STATUSES,
  isNewSubscriber,
  setEditingSubscriber,
  subscribers,
  query,
  deleteItem,
  updateItem,
  setFormData,
  setIsDialogOpen
}) {
  // Filter subscribers based on search query
  const filteredSubscribers = useMemo(() => {
    if (!subscribers) return [];
    if (!query) return subscribers;

    const q = query.toLowerCase();
    return subscribers.filter(
      (subscriber) =>
        subscriber.email?.toLowerCase().includes(q)
    );
  }, [subscribers, query]);

  // Handle subscription status change
  const handleStatusChange = async (subscriberId, newStatus) => {
    try {
      await updateItem(subscriberId, { status: newStatus });
      toast.success("Subscription status updated successfully");
    } catch (error) {
      toast.error("Failed to update subscription status");
      console.error(error);
    }
  };

  // Group subscribers by status
  const groupedSubscribers = useMemo(() => {
    const grouped = {};
    STATUSES.forEach((status) => {
      grouped[status.value] = filteredSubscribers.filter(status.filter);
    });
    return grouped;
  }, [filteredSubscribers]);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
      {STATUSES.map((status) => {
        const StatusIcon = status.icon;
        const statusSubscribers = groupedSubscribers[status.value] || [];

        return (
          <Card key={status.value} className="glass">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`p-2 rounded-lg ${status.color} text-white`}>
                    <StatusIcon className="h-4 w-4" />
                  </div>
                  <span className="text-sm font-medium">{status.label}</span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  {statusSubscribers.length}
                </Badge>
              </CardTitle>
            </CardHeader>

            <CardContent className="space-y-3">
              {statusSubscribers.map((subscriber) => (
                <Card key={subscriber.id} className="card-hover border-border/50">
                  <CardContent className="p-3 sm:p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <Mail className="h-3 w-3 text-muted-foreground shrink-0" />
                            <span className="font-medium text-sm truncate">
                              {subscriber.email}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            {subscriber.subscribed ? (
                              <Badge variant="default" className="text-xs">
                                <CheckCircle className="h-2 w-2 mr-1" />
                                Subscribed
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="text-xs">
                                <XCircle className="h-2 w-2 mr-1" />
                                Unsubscribed
                              </Badge>
                            )}
                            {isNewSubscriber(subscriber) && (
                              <Badge variant="outline" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-1 shrink-0">
                          <NewsletterEdit
                            subscriber={subscriber}
                            setEditingSubscriber={setEditingSubscriber}
                            setFormData={setFormData}
                            setIsDialogOpen={setIsDialogOpen}
                          />
                          <NewsletterDelete subscriber={subscriber} deleteItem={deleteItem} />
                        </div>
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t border-border/50">
                        <div className="text-xs text-muted-foreground">
                          {new Date(subscriber.created).toLocaleDateString()}
                        </div>

                        <Select
                          value={subscriber?.status}
                          onValueChange={(newStatus) => handleStatusChange(subscriber.id, newStatus)}
                        >
                          <SelectTrigger className="h-6 w-auto text-xs border-none p-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {STATUSES.map((s) => (
                              <SelectItem key={s.value} value={s.value} className="text-xs">
                                {s.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {statusSubscribers.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <StatusIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No subscribers</p>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  )
};
