import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Clock, UserCheck, UserPlus, Users, UserX } from "lucide-react";
import { useMemo } from "react";

export default function NewsletterStats({
  subscribers,
  isNewSubscriber,
}) {

  // Calculate statistics
  const stats = useMemo(() => {
    if (!subscribers) return { total: 0, active: 0, pending: 0, unsubscribed: 0, newSubscribers: 0 };

    const active = subscribers.filter(s => s.subscribed).length;
    const unsubscribed = subscribers.filter(s => !s.subscribed).length;
    const newSubscribers = subscribers.filter(s => isNewSubscriber(s) && s.subscribed).length;
    const pending = subscribers.filter(s => !s.subscribed && isNewSubscriber(s)).length;

    return {
      total: subscribers.length,
      active,
      pending,
      unsubscribed,
      newSubscribers
    };
  }, [subscribers]);

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <Users className="h-4 w-4" />
            Total Subscribers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            Active
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.active}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            New This Week
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{stats.newSubscribers}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pending
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
        </CardContent>
      </Card>

      <Card className="glass card-hover">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
            <UserX className="h-4 w-4" />
            Unsubscribed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.unsubscribed}</div>
        </CardContent>
      </Card>
    </div>
  )
};
