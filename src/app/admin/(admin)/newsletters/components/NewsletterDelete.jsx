import { But<PERSON> } from "@/components/ui/button";
import { XCircle } from "lucide-react";

export default function NewsletterDelete({ subscriber, deleteItem }) {
  // Handle delete
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this subscriber?")) {
      try {
        await deleteItem(id);
        toast.success("Subscriber deleted successfully");
      } catch (error) {
        toast.error("Failed to delete subscriber");
        console.error(error);
      }
    }
  };

  return (
    <Button
      size="sm"
      variant="ghost"
      onClick={() => handleDelete(subscriber.id)}
      className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
      title="Delete subscriber"
    >
      <XCircle className="h-3 w-3" />
    </Button>
  )
};
