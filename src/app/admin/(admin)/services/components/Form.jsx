import { useState } from "react";
import { Upload, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";
import ListInput from "@/components/ui/list-input";

export default function Form() {
  const { createItem, mutation } = useCollection('services');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    color: '',
    icon: null,
  });
  const [features, setFeatures] = useState([]);

  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value, files, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'file' ? files?.[0] : value,
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData((prev) => ({
      ...prev,
      icon: file
    }));
  };

  const handleReset = () => {
    setFormData({
      title: '',
      description: '',
      features: [],
      price: '',
      color: '',
      icon: null,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = new FormData();
      if (formData.icon) payload.append('icon', formData.icon);
      payload.append('title', formData.title || '');
      payload.append('description', formData.description || '');
      payload.append('features', JSON.stringify((features || []).filter(Boolean)));
      payload.append('price', formData.price || '');
      payload.append('color', formData.color || '');

      const newService = await createItem(payload);
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.CREATE,
        module: `Service`,
        details: newService
      });
      toast.success('Added the new service');
    } catch (error) {
      console.log(error)
      toast.error(error.message);
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      className='max-w-2xl'
    >
      <DialogTrigger asChild>
        <Button
          className='rounded-md text-sm'
        >
          <p>Add Service</p>
          <Plus className='w-5 h-5' />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Service</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter service title"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Price</Label>
            <Input
              type="text"
              name="price"
              value={formData.price}
              onChange={handleChange}
              placeholder="e.g., Starting at $2,500/month"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Short description"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Color</Label>
            <Input
              type="text"
              name="color"
              value={formData.color}
              onChange={handleChange}
              placeholder="e.g., blue-500"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Icon (image)</Label>
            <Input
              type="file"
              accept="image/*"
              name="icon"
              onChange={handleFileChange}
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Features</Label>
            <ListInput
              items={features}
              placeholder='eg; Custom Development'
              onChange={setFeatures}
            />
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
            >
              <p>Add Service</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
