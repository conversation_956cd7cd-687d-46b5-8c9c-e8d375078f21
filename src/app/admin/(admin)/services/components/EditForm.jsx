import { useState } from "react";
import { Upload, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";
import ListInput from "@/components/ui/list-input";

export default function EditForm(
  {
    info = { id: '', title: '', description: '', features: [], price: '', color: '', icon: '' }
  }
) {

  const { updateItem, mutation } = useCollection('services');
  const [formData, setFormData] = useState(info);
  const [features, setFeatures] = useState(info?.features)
  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleReset = () => {
    setFormData({
      title: '',
      description: '',
      features: [],
      price: '',
      color: '',
      icon: '',
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const updatedService = await updateItem(formData.id, {
        title: formData.title,
        description: formData.description,
        features: features,
        price: formData.price,
        color: formData.color,
      });
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.EDIT,
        module: `Service`,
        details: updatedService
      });
      toast.success('Updated the service');
    } catch (error) {
      console.log(error)
      toast.error(error.message);
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      className='bg-accent w-full cursor-pointer max-w-2xl'
    >
      <DialogTrigger asChild>
        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        >
          <p>Edit Details</p>
          <Pencil
            size={18}
            className="cursor-pointer"
          />
        </button>
      </DialogTrigger>
      <DialogContent className={'bg-accent max-w-2xl'}>
        <DialogHeader>
          <DialogTitle>Update Service</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter service title"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Price</Label>
            <Input
              type="text"
              name="price"
              value={formData.price}
              onChange={handleChange}
              placeholder="e.g., Starting at $2,500/month"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Short description"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Color</Label>
            <Input
              type="text"
              name="color"
              value={formData.color}
              onChange={handleChange}
              placeholder="e.g., blue-500"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Features</Label>
            <ListInput
              items={features}
              placeholder='eg; Custom Development'
              onChange={setFeatures}
            />
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
            >
              <p>Update Service</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
};
