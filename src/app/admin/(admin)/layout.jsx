'use client'

import React, { useMemo } from 'react'
import { usePathname } from 'next/navigation'

import {
  <PERSON>readcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { AppSidebar } from '../components/app-sidebar'
import { ModeToggle } from '@/components/ui/mode-toggle'

function toTitleCase(slug) {
  return slug
    .split('-')
    .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
    .join(' ')
}

function useAdminBreadcrumbs() {
  const pathname = usePathname() || '/admin'

  return useMemo(() => {
    const segments = pathname.split('/').filter(Boolean)
    // Find the index of 'admin' to start breadcrumbs from there
    const adminIndex = segments.indexOf('admin')
    const trail = []

    if (adminIndex !== -1) {
      const basePath = '/' + segments.slice(0, adminIndex + 1).join('/')
      trail.push({ href: basePath, label: 'Admin' })

      let acc = basePath
      const labelMap = {
        dashboard: 'Dashboard',
        posts: 'Posts',
        comments: 'Comments',
        profile: 'Profile',
        settings: 'Settings',
      }

      const rest = segments.slice(adminIndex + 1)
      rest.forEach((seg, i) => {
        acc += `/${seg}`
        const isLast = i === rest.length - 1
        const label = labelMap[seg] || toTitleCase(seg)
        trail.push({ href: acc, label, isLast })
      })
    }

    // Mark last item as current page
    if (trail.length) {
      trail.forEach((item, idx) => {
        item.isLast = idx === trail.length - 1
      })
    }

    return trail
  }, [pathname])
}

export default function AdminLayout({ children }) {
  const breadcrumbs = useAdminBreadcrumbs()

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className='w-full flex items-center justify-between gap-4 pr-4'>
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  {breadcrumbs.map((item, idx) => (
                    <React.Fragment key={`crumb-${idx}`}>
                      <BreadcrumbItem className={idx === 0 ? 'hidden md:block' : undefined}>
                        {item.isLast ? (
                          <BreadcrumbPage>{item.label}</BreadcrumbPage>
                        ) : (
                          <BreadcrumbLink href={item.href}>{item.label}</BreadcrumbLink>
                        )}
                      </BreadcrumbItem>
                      {idx < breadcrumbs.length - 1 && (
                        <BreadcrumbSeparator className={idx === 0 ? 'hidden md:block' : undefined} />
                      )}
                    </React.Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            <ModeToggle />
          </div>
        </header>
        <Separator orientation="horizontal" />
        <div className="bg-secondary dark:bg-background flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}
