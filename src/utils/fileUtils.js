import { FileText, Image as ImageIcon, File } from 'lucide-react';
import { toast } from 'sonner';
import pbclient from '@/lib/db';

/**
 * Determines the file type based on file extension
 * @param {string} filename - The filename to analyze
 * @returns {string} - 'image', 'document', or 'other'
 */
export const getFileType = (filename) => {
  const extension = filename.split('.').pop()?.toLowerCase();
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
  const documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx'];

  if (imageExtensions.includes(extension)) return 'image';
  if (documentExtensions.includes(extension)) return 'document';
  return 'other';
};

/**
 * Returns the appropriate icon component for a file based on its type
 * @param {string} filename - The filename to analyze
 * @returns {React.Component} - Lucide icon component
 */
export const getFileIcon = (filename) => {
  const fileType = getFileType(filename);

  switch (fileType) {
    case 'image':
      return ImageIcon;
    case 'document':
      return FileText;
    default:
      return File;
  }
};

/**
 * Downloads a single file
 * @param {string} filename - The filename to download
 * @param {Object} order - The order object containing file data
 */
export const downloadFile = (filename, order) => {
  if (!order) {
    toast.error('Order data not available');
    return;
  }

  try {
    const url = pbclient.files.getURL(order, filename);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success(`Downloading ${filename}`);
  } catch (error) {
    console.error('Error downloading file:', error);
    toast.error(`Failed to download ${filename}`);
  }
};

/**
 * Processes files array to include type information
 * @param {Array} fileList - Array of filenames
 * @param {Object} order - Order object for generating URLs
 * @returns {Array} - Array of file objects with url, filename, and type properties
 */
export const processFiles = (fileList, order) => {
  if (!Array.isArray(fileList) || !order) return [];

  return fileList.map(filename => ({
    filename,
    url: pbclient.files.getURL(order, filename),
    type: getFileType(filename)
  }));
};
