export default function extractNumberFromTimeText(text) {
  let hours = 0, minutes = 0, seconds = 0;

  let hMatch = text.match(/(\d+)\s*hour/);
  let mMatch = text.match(/(\d+)\s*min/);
  let sMatch = text.match(/(\d+)\s*sec/);

  if (hMatch) hours = parseInt(hMatch[1]);
  if (mMatch) minutes = parseInt(mMatch[1]);
  if (sMatch) seconds = parseInt(sMatch[1]);

  let totalMinutes = hours * 60 + minutes;
  if (seconds > 0) totalMinutes += 1; // round up if seconds exist

  return totalMinutes;
}
