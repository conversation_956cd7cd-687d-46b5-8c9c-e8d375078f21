export default function timeAgo(date) {
  const now = new Date();
  const seconds = Math.floor((now - date) / 1000);

  let value, unit;

  if (seconds < 60) {
    value = -seconds;
    unit = 'second';
  } else if (seconds < 3600) {
    value = -Math.floor(seconds / 60);
    unit = 'minute';
  } else if (seconds < 86400) {
    value = -Math.floor(seconds / 3600);
    unit = 'hour';
  } else if (seconds < 2592000) {
    value = -Math.floor(seconds / 86400);
    unit = 'day';
  } else if (seconds < 31536000) {
    value = -Math.floor(seconds / 2592000);
    unit = 'month';
  } else {
    value = -Math.floor(seconds / 31536000);
    unit = 'year';
  }

  return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(value, unit);
}
