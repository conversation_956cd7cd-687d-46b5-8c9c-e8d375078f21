import React from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { EllipsisVertical, Trash } from 'lucide-react'
import { Button } from '../ui/button';

export default function DetailsActions({
  row,
  EditForm,
  deleteItem,
}) {
  return (
    <Popover
    >
      <PopoverTrigger asChild>
        <Button variant="outline" size={'icon'}>
          <EllipsisVertical className="w-4 h-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[250px] -p-2 rounded-lg">
        <h1 className='font-semibold text-lg text-left border-b border-foreground/30 p-4'>Actions</h1>

        {EditForm && <EditForm info={row.original} />}

        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
          onClick={async () => {
            console.log('Delete details for', row.original.id);
            const confirmation = confirm('Are you sure you want to delete this entry?');
            if (confirmation) {
              await deleteItem(row.original.id);
            }
          }}
        >
          <p>Delete Entry</p>
          <Trash
            size={18}
            className="cursor-pointer"
          />
        </button>
      </PopoverContent>
    </Popover>
  )
};
