import React, { useRef } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { ArrowUpRight, Copy, EllipsisVertical, Replace, Trash } from 'lucide-react'
import { Button } from '../ui/button';
import Link from 'next/link';
import pbclient from '@/lib/db';
import { toast } from 'sonner';
import { createGeneralAuditLog } from '@/utils/auditLogger';
import { AUDIT_ACTIONS } from '@/constants/audit';

export default function BlogsActions({
  row,
  EditForm,
  deleteItem,
}) {
  const fileInputRef = useRef(null);

  const handleCopyImageLinks = async () => {
    try {
      const assets = row?.original?.assests || [];
      if (!assets.length) {
        toast.info('No assets to copy');
        return;
      }
      const urls = assets.map((a) => pbclient.files.getURL(row.original, a));
      await navigator.clipboard.writeText(urls.join('\n'));
      toast.success('Image links copied to clipboard');
    } catch (e) {
      console.error(e);
      toast.error('Failed to copy links');
    }
  };

  const triggerReplace = () => fileInputRef.current?.click();

  const handleReplace = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    try {
      const payload = new FormData();
      payload.append('content', file);
      const updated = await pbclient.collection('blogs').update(row.original.id, payload);
      await createGeneralAuditLog({ action: AUDIT_ACTIONS.EDIT, module: 'Blogs', details: updated });
      toast.success('Article content replaced');
    } catch (err) {
      console.error(err);
      toast.error(err?.message || 'Failed to replace article');
    } finally {
      e.target.value = '';
    }
  };

  const adminPreviewHref = `/admin/blogs/${row?.original?.slug}`;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size={'icon'}>
          <EllipsisVertical className="w-4 h-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[250px] -p-2 rounded-lg">
        <h1 className='font-semibold text-lg text-left border-b border-foreground/30 p-4'>Actions</h1>

        <Link href={adminPreviewHref}>
          <button className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'>
            <p>Preview (Admin)</p>
            <ArrowUpRight size={18} className="cursor-pointer" />
          </button>
        </Link>

        <button className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer' onClick={handleCopyImageLinks}>
          <p>Copy Image Links</p>
          <Copy size={18} className="cursor-pointer" />
        </button>

        <input ref={fileInputRef} type="file" accept=".md,.markdown,.mdx,text/markdown,text/plain" onChange={handleReplace} className="hidden" />
        <button className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer' onClick={triggerReplace}>
          <p>Replace Article</p>
          <Replace size={18} className="cursor-pointer" />
        </button>

        {EditForm && <EditForm info={row.original} />}

        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
          onClick={async () => {
            try {
              const confirmation = confirm('Are you sure you want to delete this entry?');
              if (confirmation) {
                await deleteItem(row.original.id);
                toast.success('Entry deleted');
              }
            } catch (err) {
              console.error(err);
              toast.error('Failed to delete');
            }
          }}
        >
          <p>Delete Entry</p>
          <Trash size={18} className="cursor-pointer" />
        </button>
      </PopoverContent>
    </Popover>
  )
};
