'use client';

import { useEffect } from 'react';

/**
 * ResourcePreloader component for preloading critical resources
 * Implements Lighthouse recommendations for reducing document request latency
 */
export function ResourcePreloader() {
  useEffect(() => {
    // Preload critical fonts
    const preloadFonts = () => {
      const fonts = [
        {
          href: 'https://fonts.gstatic.com/s/plusjakartasans/v8/LDIbaomQNQcsA88c7O9yZ4KMCoOg4IA6-91aHEjcWuA_qU79TR_VfMQ.woff2',
          type: 'font/woff2'
        },
        {
          href: 'https://fonts.gstatic.com/s/redhatdisplay/v19/8vIf7wUr0m80wwYf0QCXZzYzUoTK8RZQvRd-D1NYbjKKVrjzXw.woff2',
          type: 'font/woff2'
        }
      ];

      fonts.forEach(font => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = font.href;
        link.as = 'font';
        link.type = font.type;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
    };

    // Preload critical images
    const preloadImages = () => {
      const images = [
        '/og-image.jpg',
        '/favicon.ico',
        '/apple-touch-icon.png'
      ];

      images.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = src;
        link.as = 'image';
        document.head.appendChild(link);
      });
    };

    // Preload critical CSS
    const preloadCriticalCSS = () => {
      const criticalCSS = `
        /* Critical above-the-fold styles */
        .hero-section {
          min-height: 80vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .navbar {
          position: sticky;
          top: 0;
          z-index: 50;
          backdrop-filter: blur(8px);
        }
        
        .loading-skeleton {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
        }
        
        @keyframes skeleton-loading {
          0% { background-position: 200% 0; }
          100% { background-position: -200% 0; }
        }
        
        /* Optimize font loading */
        .font-display-swap {
          font-display: swap;
        }
        
        /* Reduce layout shift */
        .aspect-ratio-16-9 {
          aspect-ratio: 16 / 9;
        }
        
        .aspect-ratio-4-3 {
          aspect-ratio: 4 / 3;
        }
        
        .aspect-ratio-1-1 {
          aspect-ratio: 1 / 1;
        }
      `;

      const style = document.createElement('style');
      style.setAttribute('data-critical', 'true');
      style.textContent = criticalCSS;
      document.head.appendChild(style);
    };

    // DNS prefetch for external domains
    const prefetchDNS = () => {
      const domains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://www.google-analytics.com',
        'https://www.googletagmanager.com'
      ];

      domains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = domain;
        document.head.appendChild(link);
      });
    };

    // Preconnect to critical origins
    const preconnectOrigins = () => {
      const origins = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
      ];

      origins.forEach(origin => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = origin;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
    };

    // Module preload for critical JavaScript
    const preloadModules = () => {
      const modules = [
        '/_next/static/chunks/main.js',
        '/_next/static/chunks/webpack.js'
      ];

      modules.forEach(module => {
        const link = document.createElement('link');
        link.rel = 'modulepreload';
        link.href = module;
        document.head.appendChild(link);
      });
    };

    // Execute preloading strategies
    preloadFonts();
    preloadImages();
    prefetchDNS();
    preconnectOrigins();
    preloadModules();

    // Cleanup function
    return () => {
      // Remove preload links after they're no longer needed
      const preloadLinks = document.querySelectorAll('link[rel="preload"], link[rel="dns-prefetch"], link[rel="preconnect"], link[rel="modulepreload"]');
      preloadLinks.forEach(link => {
        // Keep critical resources, remove others after 10 seconds
        if (!link.href.includes('font') && !link.href.includes('critical')) {
          setTimeout(() => {
            if (link.parentNode) {
              link.parentNode.removeChild(link);
            }
          }, 10000);
        }
      });
    };
  }, []);

  return null; // This component doesn't render anything
}

/**
 * Critical CSS injector for above-the-fold content
 */
export function CriticalCSS() {
  useEffect(() => {
    // Inject critical CSS if not already present
    if (!document.querySelector('style[data-critical-inline]')) {
      const style = document.createElement('style');
      style.setAttribute('data-critical-inline', 'true');
      style.textContent = `
        /* Prevent layout shift */
        img, video, iframe {
          max-width: 100%;
          height: auto;
        }
        
        /* Optimize font loading */
        @font-face {
          font-family: 'Plus Jakarta Sans';
          font-display: swap;
        }
        
        @font-face {
          font-family: 'Red Hat Display';
          font-display: swap;
        }
        
        /* Critical layout styles */
        .container-modern {
          max-width: min(1400px, calc(100vw - 2rem));
          margin: 0 auto;
          padding: 0 1rem;
        }
        
        @media (min-width: 640px) {
          .container-modern {
            padding: 0 1.5rem;
          }
        }
        
        @media (min-width: 1024px) {
          .container-modern {
            padding: 0 2rem;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  return null;
}

/**
 * Performance monitoring and Core Web Vitals tracking
 */
export function PerformanceMonitor() {
  useEffect(() => {
   

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn('Long task detected:', entry);
          }
        });
      });
      
      observer.observe({ entryTypes: ['longtask'] });
      
      return () => observer.disconnect();
    }
  }, []);

  return null;
}
