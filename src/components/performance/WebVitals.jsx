'use client';

import { useEffect } from 'react';

/**
 * Web Vitals monitoring component
 * Tracks Core Web Vitals and sends data to analytics
 */
export function WebVitals() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    // Dynamic import to avoid bundling web-vitals in development
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      // Function to send vitals to analytics
      const sendToAnalytics = (metric) => {
        // Send to Google Analytics 4
        if (typeof gtag !== 'undefined') {
          gtag('event', metric.name, {
            event_category: 'Web Vitals',
            event_label: metric.id,
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            non_interaction: true,
          });
        }

        // Send to console in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Web Vital:', metric);
        }

        // You can also send to your own analytics service
        // fetch('/api/analytics/web-vitals', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(metric),
        // });
      };

      // Track all Core Web Vitals
      getCLS(sendToAnalytics);
      getFID(sendToAnalytics);
      getFCP(sendToAnalytics);
      getLCP(sendToAnalytics);
      getTTFB(sendToAnalytics);
    }).catch(() => {
      // Silently fail if web-vitals is not available
    });
  }, []);

  return null;
}

/**
 * Performance observer for monitoring long tasks
 */
export function LongTaskMonitor() {
  useEffect(() => {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) {
          console.warn('Long task detected:', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name,
          });

          // Send to analytics if needed
          if (typeof gtag !== 'undefined') {
            gtag('event', 'long_task', {
              event_category: 'Performance',
              event_label: 'Long Task',
              value: Math.round(entry.duration),
              custom_map: {
                duration: entry.duration,
                start_time: entry.startTime,
              },
            });
          }
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      // Silently fail if longtask is not supported
    }

    return () => observer.disconnect();
  }, []);

  return null;
}

/**
 * Layout shift monitor
 */
export function LayoutShiftMonitor() {
  useEffect(() => {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.hadRecentInput) return; // Ignore shifts after user input

        if (entry.value > 0.1) {
          console.warn('Significant layout shift detected:', {
            value: entry.value,
            sources: entry.sources,
          });

          // Send to analytics
          if (typeof gtag !== 'undefined') {
            gtag('event', 'layout_shift', {
              event_category: 'Performance',
              event_label: 'Layout Shift',
              value: Math.round(entry.value * 1000),
            });
          }
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      // Silently fail if layout-shift is not supported
    }

    return () => observer.disconnect();
  }, []);

  return null;
}

/**
 * Resource timing monitor
 */
export function ResourceTimingMonitor() {
  useEffect(() => {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        // Monitor slow resources (>1s)
        if (entry.duration > 1000) {
          console.warn('Slow resource detected:', {
            name: entry.name,
            duration: entry.duration,
            transferSize: entry.transferSize,
          });
        }

        // Monitor large resources (>500KB)
        if (entry.transferSize > 500000) {
          console.warn('Large resource detected:', {
            name: entry.name,
            transferSize: entry.transferSize,
            duration: entry.duration,
          });
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['resource'] });
    } catch (e) {
      // Silently fail if resource timing is not supported
    }

    return () => observer.disconnect();
  }, []);

  return null;
}

/**
 * Combined performance monitoring component
 */
export function PerformanceMonitoring() {
  return (
    <>
      <WebVitals />
      <LongTaskMonitor />
      <LayoutShiftMonitor />
      <ResourceTimingMonitor />
    </>
  );
}

/**
 * Performance budget checker
 */
export function PerformanceBudget() {
  useEffect(() => {
    // Check performance budget after page load
    const checkBudget = () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      if (navigation) {
        const metrics = {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstByte: navigation.responseStart - navigation.requestStart,
        };

        // Define budget thresholds
        const budget = {
          domContentLoaded: 1500, // 1.5s
          loadComplete: 3000,     // 3s
          firstByte: 200,         // 200ms
        };

        // Check against budget
        Object.entries(metrics).forEach(([metric, value]) => {
          if (value > budget[metric]) {
            console.warn(`Performance budget exceeded for ${metric}:`, {
              actual: value,
              budget: budget[metric],
              overage: value - budget[metric],
            });
          }
        });
      }
    };

    // Check budget after load
    if (document.readyState === 'complete') {
      checkBudget();
    } else {
      window.addEventListener('load', checkBudget);
      return () => window.removeEventListener('load', checkBudget);
    }
  }, []);

  return null;
}
