import {
  Mail,
  Phone,
  MapPin,
} from 'lucide-react';

export default function ContactInfo() {
  const contactInfo = {
    mail: '<EMAIL>',
    contact: '+****************',
    location: 'Dombivli',
  };

  return (
    <>
      <h2 className="text-headline mb-6">Let's Start a Conversation</h2>
      <p className="text-muted-foreground md:text-lg text-base mb-8 leading-relaxed">
        Ready to take your content and digital presence to the next level?
        We'd love to hear about your project and explore how we can help you achieve your goals.
      </p>

      <div className="space-y-6 mb-8">
        <div className="flex items-center gap-4">
          <div className="md:w-12 md:h-12 w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 md:rounded-xl rounded-md flex items-center justify-center">
            <Mail className="md:w-6 md:h-6 w-4 h-4 text-primary" />
          </div>
          <div>
            <h4 className="font-semibold mb-1 md:text-base text-sm">Email Us</h4>
            <p className="text-muted-foreground md:text-base text-sm">{contactInfo.mail}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="md:w-12 md:h-12 w-8 h-8 bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-xl flex items-center justify-center">
            <Phone className="md:w-6 md:h-6 w-4 h-4 text-green-500" />
          </div>
          <div>
            <h4 className="font-semibold mb-1 md:text-base text-sm">Call Us</h4>
            <p className="text-muted-foreground md:text-base text-sm">{contactInfo.contact}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="md:w-12 md:h-12 w-8 h-8 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-xl flex items-center justify-center">
            <MapPin className="md:w-6 md:h-6 w-4 h-4 text-purple-500" />
          </div>
          <div>
            <h4 className="font-semibold mb-1 md:text-base text-sm">Visit Us</h4>
            <p className="text-muted-foreground md:text-base text-sm">{contactInfo.location}</p>
          </div>
        </div>
      </div>
    </>
  )
};
