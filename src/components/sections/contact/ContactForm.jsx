import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Send
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import pbclient from '@/lib/db';
import { useCollection } from '@/hooks/useCollection';
import { toast } from 'sonner';

export default function ContactForm({ services }) {
  const { createItem } = useCollection('inquiries');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    companyName: '',
    service: '',
    budget: '',
    timeline: '',
    description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await createItem(formData);
    toast.success('Thank you! We\'ll get back to you within 24 hours.');
    setFormData({
      name: '',
      email: '',
      companyName: '',
      service: '',
      budget: '',
      timeline: '',
      description: '',
      status: 'pending'
    });
    setIsSubmitting(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      <Card className="border-0 shadow-2xl bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Start Your Project</CardTitle>
          <CardDescription>
            Tell us about your needs and we'll create a custom proposal for you.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 text-sm">
                  Name<span className='text-red-500'>*</span>
                </Label>
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Your full name"
                  required
                  className="h-12"
                />
              </div>
              <div>
                <Label className="mb-2 text-sm">
                  Email<span className='text-red-500'>*</span>
                </Label>
                <Input
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  required
                  className="h-12"
                />
              </div>
            </div>

            <div>
              <Label className="mb-2 text-sm"> Company Name </Label>
              <Input
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                placeholder="Your Company Name"
                className="h-12"
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 text-sm">
                  Service Interested In<span className='text-red-500'>*</span>
                </Label>
                <Select value={formData?.service} onValueChange={(value) => setFormData({ ...formData, service: value })}>
                  <SelectTrigger className="w-full h-12 px-3 rounded-md border border-input bg-background text-sm">
                    <SelectValue placeholder="Select a service" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Services</SelectLabel>
                      {services?.map((service, index) => (
                        <SelectItem
                          key={index}
                          value={service?.id}
                        >
                          <div className="w-8 h-8 relative overflow-hidden flex-shrink-0">
                            <LazyLoadingImage
                              src={pbclient?.files?.getURL(service, service?.icon)}
                              alt={service.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          {service.title}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 text-sm">
                  Budget Range<span className='text-red-500'>*</span>
                </Label>
                <Select value={formData?.budget} onValueChange={(value) => setFormData({ ...formData, budget: value })}>
                  <SelectTrigger className="w-full h-12 px-3 rounded-md border border-input bg-background text-sm">
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Budget</SelectLabel>
                      <SelectItem value="Rather Not Say">Rather Not Say</SelectItem>
                      <SelectItem value="Under 1k">Under 1k</SelectItem>
                      <SelectItem value="1k-10k">1k - 10k</SelectItem>
                      <SelectItem value="10k-20k">10k - 20k</SelectItem>
                      <SelectItem value="20k-50k">20k - 50k</SelectItem>
                      <SelectItem value="Above 50k">Above 50k</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="mb-2 text-sm">
                Project Timeline<span className='text-red-500'>*</span>
              </Label>
              <Select value={formData?.timeline} onValueChange={(value) => setFormData({ ...formData, timeline: value })}>
                <SelectTrigger className="w-full h-12 px-3 rounded-md border border-input bg-background text-sm">
                  <SelectValue placeholder="Select timeline" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Project Timeline</SelectLabel>
                    <SelectItem value="Immediate">Immediate</SelectItem>
                    <SelectItem value="Within 1 month">Within 1 month</SelectItem>
                    <SelectItem value="Within 3 months">Within 3 months</SelectItem>
                    <SelectItem value="Within 6 months">Within 6 months</SelectItem>
                    <SelectItem value="Flexible">Flexible</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="mb-2 text-sm">
                Project Description<span className='text-red-500'>*</span>
              </Label>
              <Textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Tell us about your project, goals, and any specific requirements..."
                required
                rows={4}
                className="resize-none"
              />
            </div>

            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full btn-modern h-12 text-base"
            >
              {isSubmitting ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <Send className="w-5 h-5" />
                </motion.div>
              ) : (
                <Send className="w-5 h-5 mr-2" />
              )}
              {isSubmitting ? 'Sending...' : 'Send Proposal Request'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
};
