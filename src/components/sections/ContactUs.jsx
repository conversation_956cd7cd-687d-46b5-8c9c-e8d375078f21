import { motion } from 'motion/react';
import ContactInfo from './contact/ContactInfo';
import ContactPreview from './contact/ContactPreview';
import ContactForm from './contact/ContactForm';

export default function ContactUs({ services }) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-muted/30"
    >
      <div className="container-modern">
        <div className="grid lg:grid-cols-2 gap-16 items-start">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {/* Contact Info */}
            <ContactInfo />

            {/* Team Preview */}
            <ContactPreview />
          </motion.div>

          {/* Contact Form */}
          <ContactForm services={services} />
        </div>
      </div>
    </motion.section>
  )
};
