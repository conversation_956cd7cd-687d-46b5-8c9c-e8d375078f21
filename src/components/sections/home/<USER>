import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { ChevronLeft, ChevronRight, Clock, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { HeroImage } from '@/components/blocks/OptimizedImage'
import pbclient from '@/lib/db'
import slugify from '@/utils/slugify'
import timeAgo from '@/utils/timeAgo'

export function HeroCarousel({ featuredArticles }) {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    if (featuredArticles.length === 0) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
    }, 10000)

    return () => clearInterval(interval)
  }, [featuredArticles])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
  }

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + featuredArticles.length) % featuredArticles.length
    )
  }

  const slide = featuredArticles[currentSlide];
  if (!slide) {
    return (
      <section className="relative h-[80dvh] w-full overflow-hidden bg-gray-800 flex items-center justify-center">
        <motion.div
          className="w-full h-full flex items-center justify-center"
          initial={{ opacity: 0.5 }}
          animate={{ opacity: 1 }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 1
          }}
        >
          <div className="max-w-2xl w-full space-y-6 p-4">
            {/* Category badge skeleton */}
            <div className="h-6 w-24 bg-gray-600 rounded-full" />

            {/* Title skeleton */}
            <div className="h-12 w-3/4 bg-gray-600 rounded" />
            <div className="h-12 w-1/2 bg-gray-600 rounded" />

            {/* Description skeleton */}
            <div className="h-4 w-full bg-gray-600 rounded" />
            <div className="h-4 w-5/6 bg-gray-600 rounded" />

            {/* Author + meta skeleton */}
            <div className="flex items-center gap-4 mt-4">
              <div className="w-10 h-10 bg-gray-600 rounded-full" />
              <div className="h-4 w-32 bg-gray-600 rounded" />
            </div>

            {/* Button skeleton */}
            <div className="h-10 w-40 bg-gray-600 rounded mt-6" />
          </div>
        </motion.div>
      </section>
    )
  }

  return (
    <section className="relative h-[80dvh] w-full overflow-hidden touch-action-pan-y">
      <AnimatePresence mode="wait">
        <motion.div
          key={slide.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="absolute inset-0"
        >
          <HeroImage
            src={pbclient.files.getURL(slide, slide?.thumbnail) || '/placeholder.png'}
            alt={slide.title}
            className="w-full h-full object-cover pointer-events-none select-none"
            priority={currentSlide === 0} // Prioritize first slide for LCP
            fill={true}
          />
          <div className="absolute inset-0 bg-black/70 pointer-events-none" />

          <div className="absolute inset-0 flex items-center justify-center pointer-events-auto">
            <div className="flex items-center justify-center px-4">
              <div className="max-w-2xl text-white space-y-6">
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <Badge variant={'secondary'} className={'px-4'}>
                    {slide?.expand?.category?.title}
                  </Badge>
                </motion.div>

                <motion.h1
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl md:text-5xl lg:text-6xl font-bold leading-tight"
                >
                  {slide.title}
                </motion.h1>

                <motion.p
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-lg text-gray-200"
                >
                  {slide.description}
                </motion.p>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center md:flex-row flex-col gap-4 text-sm text-gray-300"
                >
                  <Button variant={'link'} className={'text-white font-semibold flex items-center'}>
                    <Avatar>
                      <AvatarImage
                        src={
                          pbclient.files.getURL(slide.expand.author, slide.expand.author.avatar) ||
                          '/placeholder.png'
                        }
                        alt={slide?.expand?.author?.name}
                      />
                      <AvatarFallback>
                        {slide?.expand?.author?.name?.charAt(0)?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <Link
                      href={`/author/${slugify(slide?.expand?.author?.name?.toLowerCase())}`}
                      className='flex items-center'
                    >
                      {slide?.expand?.author?.name}
                    </Link>
                  </Button>
                  <div className='flex items-center gap-4'>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {slide.readTime}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {slide.views?.toLocaleString()}
                    </span>
                    <span>{timeAgo(new Date(slide.created))}</span>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className='md:text-left text-center'
                >
                  <Link href={`/blogs/${slide.slug}`}>
                    <Button
                      size="lg"
                      className="bg-white/10 text-white border-white/30 hover:bg-white/20 hover:border-white/50 hover:scale-105 transition-all duration-300 backdrop-blur-md shadow-lg"
                    >
                      Read Full Article
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        size="icon"
        onClick={prevSlide}
        className="md:flex absolute hidden left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 backdrop-blur-md shadow-lg transition-all duration-300 hover:scale-110 pointer-events-auto"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 h-6" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={nextSlide}
        className="md:flex absolute hidden right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 backdrop-blur-md shadow-lg transition-all duration-300 hover:scale-110 pointer-events-auto"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 h-6" />
      </Button>

    </section>
  )
};
