import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardD<PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Link from "next/link";
import LazyLoadingImage from "@/components/blocks/LazyLoadingImage";
import { motion } from "motion/react";
import { Clock, Eye, ArrowUpRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import pbclient from "@/lib/db";
import timeAgo from "@/utils/timeAgo";
import slugify from "@/utils/slugify";

export default function BlogsList({ blogs }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
      {blogs.map((blog, index) => (
        <motion.div
          key={blog.id}
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          viewport={{ once: true }}
          whileHover={{ y: -8 }}
          className="group"
        >
          <Card className="h-full overflow-hidden border-0 pt-0  rounded-b-xlshadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
            <Link href={`/blogs/${blog.slug}`} className="w-full h-full">
              <div className="relative overflow-hidden rounded-t-xl">
                <LazyLoadingImage
                  src={pbclient.files.getURL(blog, blog?.thumbnail) || '/placeholder.png'}
                  alt={blog.title}
                  className="h-48 w-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Floating stats */}
                <div className="absolute top-4 right-4 flex gap-2">
                  <Badge variant="secondary" className="bg-black/70 text-white border-0 backdrop-blur-sm">
                    <Eye className="w-3 h-3 mr-1" />
                    {blog?.views}
                  </Badge>
                </div>

                {/* Category badge */}
                <div className="absolute top-4 left-4">
                  <Badge variant="secondary" className="bg-black/30 text-white backdrop-blur-xl">
                    {blog?.expand?.category?.title}
                  </Badge>
                </div>

                {/* Read more button - appears on hover */}
                <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <Button size="sm" variant="secondary" className="bg-white/90 text-black hover:bg-white">
                    Read More
                    <ArrowUpRight className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              </div>

              <CardHeader className="py-4">
                <CardTitle className="text-lg line-clamp-2 leading-tight group-hover:text-primary transition-colors duration-300">
                  {blog.title}
                </CardTitle>
                <CardDescription className="line-clamp-3 text-sm leading-relaxed">
                  {blog.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Reading time and date */}
                <div className="flex items-center gap-4 justify-between text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {blog.readTime}
                  </span>
                  <span>{timeAgo(new Date(blog.created))}</span>
                </div>
              </CardContent>
            </Link>
            <CardFooter>
              <Link href={`/authors/${slugify(blog?.expand?.author?.name)}`}>
                {/* Author info */}
                <div
                  className="flex items-center gap-3 hover:text-primary transition-colors duration-300 group/author"
                >
                  <Avatar className="h-8 w-8 ring-2 ring-transparent group-hover/author:ring-primary/20 transition-all duration-300">
                    <AvatarImage
                      src={
                        pbclient.files.getURL(blog.expand.author, blog.expand.author.avatar) ||
                        '/placeholder.png'
                      }
                      alt={blog?.expand?.author?.name}
                    />
                    <AvatarFallback className="text-xs">
                      {blog?.expand?.author?.name?.charAt(0)?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {blog?.expand?.author?.name}
                    </p>
                    <p className="text-xs text-muted-foreground">Author</p>
                  </div>
                </div>
              </Link>
            </CardFooter>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

