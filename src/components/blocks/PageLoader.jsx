import "./loading.css";

export default function PageLoader({
  variant = "spinner",
  size = 48,
  color,
  message,
  className = "",
  style = {},
  skeletonWidth = "100%",
  skeletonHeight = 12,
}) {
  const sizeValue = typeof size === "number" ? `${size}px` : size;
  const skeletonW = typeof skeletonWidth === "number" ? `${skeletonWidth}px` : skeletonWidth;
  const skeletonH = typeof skeletonHeight === "number" ? `${skeletonHeight}px` : skeletonHeight;

  const inline = { ...style };
  if (color) inline["--loading-color"] = color;
  if (sizeValue) inline["--loading-size"] = sizeValue;

  return (
    <div
      className={`loading-root loading-variant-${variant} ${className} w-screen h-screen flex items-center justify-center`}
      style={inline}
      role="status"
      aria-live="polite"
      aria-busy="true"
    >

      {variant === "spinner" && (
        <div className="loading-spinner" aria-hidden="true">
          <svg
            viewBox="22 22 44 44"
            width={sizeValue}
            height={sizeValue}
            className="loading-svg"
            focusable="false"
            role="img"
          >
            <defs>
              <linearGradient id="lg" x1="0%" x2="100%">
                <stop offset="0%" stopColor="var(--loading-color, #0b74ff)" stopOpacity="0.15" />
                <stop offset="60%" stopColor="var(--loading-color, #0b74ff)" stopOpacity="0.65" />
                <stop offset="100%" stopColor="var(--loading-color, #0b74ff)" stopOpacity="1" />
              </linearGradient>
            </defs>

            <circle className="loading-track" cx="44" cy="44" r="20" fill="none" stroke="rgba(0,0,0,0.06)" strokeWidth="4" />
            <path
              className="loading-arc"
              fill="none"
              stroke="url(#lg)"
              strokeWidth="4"
              strokeLinecap="round"
              d="M44 24a20 20 0 0 1 0 40"
            />
          </svg>
        </div>
      )}

      {variant === "dots" && (
        <div className="loading-dots" aria-hidden="true" style={{ fontSize: `calc(${sizeValue} / 3.5)` }}>
          <span className="dot" />
          <span className="dot" />
          <span className="dot" />
        </div>
      )}

      {variant === "skeleton" && (
        <div className="loading-skeleton" aria-hidden="true">
          <div
            className="skeleton-bar"
            style={{ width: skeletonW, height: skeletonH }}
          />
        </div>
      )}

    </div>
  );
};
