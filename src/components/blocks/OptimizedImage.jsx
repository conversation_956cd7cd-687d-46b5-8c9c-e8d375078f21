'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

/**
 * OptimizedImage component with modern format support and performance optimizations
 * Supports WebP/AVIF formats with fallbacks, proper sizing, and accessibility
 */
export default function OptimizedImage({
  src,
  alt,
  className = '',
  width = 800,
  height = 600,
  priority = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  quality = 85,
  placeholder = 'blur',
  fill = false,
  style = {},
  onLoad,
  onError,
  ...props
}) {
  const [imageError, setImageError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Generate optimized image URLs for different formats
  const getOptimizedSrc = (originalSrc, format = 'webp') => {
    if (!originalSrc) return originalSrc;
    
    // If it's already a modern format, return as is
    if (originalSrc.includes('.webp') || originalSrc.includes('.avif')) {
      return originalSrc;
    }
    
    // For external URLs or if no optimization needed
    if (originalSrc.startsWith('http') || originalSrc.startsWith('//')) {
      return originalSrc;
    }
    
    // Generate optimized version (this would typically be handled by your image optimization service)
    const extension = originalSrc.split('.').pop();
    return originalSrc.replace(`.${extension}`, `.${format}`);
  };

  // Blur data URL for placeholder
  const blurDataURL = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==";

  const handleLoad = (e) => {
    setIsLoaded(true);
    onLoad?.(e);
  };

  const handleError = (e) => {
    setImageError(true);
    onError?.(e);
  };

  // Ensure alt text is descriptive and not redundant
  const optimizedAlt = alt || '';

  // If using fill prop, don't specify width/height
  const imageProps = fill 
    ? { fill: true }
    : { width, height };

  return (
    <div className={`relative overflow-hidden ${className}`} style={style}>
      {/* Loading placeholder */}
      {!isLoaded && !imageError && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{ 
            width: fill ? '100%' : width, 
            height: fill ? '100%' : height 
          }}
        />
      )}

      {/* Error fallback */}
      {imageError && (
        <div 
          className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-400 text-sm"
          style={{ 
            width: fill ? '100%' : width, 
            height: fill ? '100%' : height 
          }}
        >
          <span>Image unavailable</span>
        </div>
      )}

      {/* Optimized image with modern format support */}
      {!imageError && (
        <picture>
          {/* AVIF format for modern browsers */}
          <source 
            srcSet={getOptimizedSrc(src, 'avif')} 
            type="image/avif"
            sizes={sizes}
          />
          
          {/* WebP format for most browsers */}
          <source 
            srcSet={getOptimizedSrc(src, 'webp')} 
            type="image/webp"
            sizes={sizes}
          />
          
          {/* Fallback to original format */}
          <Image
            src={src}
            alt={optimizedAlt}
            {...imageProps}
            loading={priority ? "eager" : "lazy"}
            priority={priority}
            sizes={sizes}
            quality={quality}
            placeholder={placeholder}
            blurDataURL={blurDataURL}
            onLoad={handleLoad}
            onError={handleError}
            className={`transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            {...props}
          />
        </picture>
      )}
    </div>
  );
}

/**
 * Hero Image component optimized for LCP
 */
export function HeroImage({ src, alt, className = '', ...props }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      priority={true}
      sizes="100vw"
      quality={90}
      placeholder="blur"
      {...props}
    />
  );
}

/**
 * Card Image component for blog cards and similar content
 */
export function CardImage({ src, alt, className = '', ...props }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      width={400}
      height={300}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      quality={80}
      {...props}
    />
  );
}

/**
 * Avatar Image component for user profiles
 */
export function AvatarImage({ src, alt, className = '', size = 40, ...props }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={`rounded-full ${className}`}
      width={size}
      height={size}
      sizes={`${size}px`}
      quality={75}
      {...props}
    />
  );
}
