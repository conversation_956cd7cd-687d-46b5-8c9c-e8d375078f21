:root {
  --loading-color: #0b74ff;
  /* primary accent */
  --loading-size: 48px;
  --loading-gap: 12px;
  --loading-font: 14px;
}

/* Reset for the component root */
.loading-root {
  display: inline-flex;
  align-items: center;
  gap: var(--loading-gap);
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;
  font-size: var(--loading-font);
  color: #222;
  line-height: 1;
}

/* Visually hidden helper for accessibility */
.visually-hidden {
  position: absolute !important;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.loading-message {
  font-size: var(--loading-font);
  color: inherit;
}

/* =====================
   Spinner variant
===================== */
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.loading-svg {
  animation: spinner-rotate 0.8s linear infinite;
}

.loading-track {
  stroke: rgba(0, 0, 0, 0.06);
}

.loading-arc {
  transform-origin: center;
}

/* Rotation animation */
@keyframes spinner-rotate {
  100% {
    transform: rotate(360deg);
  }
}

/* =====================
   Dots variant
===================== */
.loading-dots {
  display: inline-flex;
  align-items: flex-end;
}

.loading-dots .dot {
  width: 0.6em;
  height: 0.6em;
  background-color: var(--loading-color);
  border-radius: 50%;
  margin: 0 0.15em;
  animation: bounce 0.6s infinite ease-in-out;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: 0.15s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0.6);
  }

  40% {
    transform: scale(1);
  }
}

/* =====================
   Skeleton variant
===================== */
.loading-skeleton {
  display: inline-block;
}

.skeleton-bar {
  background: linear-gradient(90deg,
      rgba(0, 0, 0, 0.06) 25%,
      rgba(0, 0, 0, 0.12) 37%,
      rgba(0, 0, 0, 0.06) 63%);
  background-size: 400% 100%;
  border-radius: 4px;
  animation: shimmer 1.4s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* =====================
   Reduced motion
===================== */
@media (prefers-reduced-motion: reduce) {

  .loading-svg,
  .loading-dots .dot,
  .skeleton-bar {
    animation: none !important;
  }
}
