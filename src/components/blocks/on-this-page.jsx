"use client"
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { BaseUrl } from '@/constants/url';
import { Link as LinkIcon, Linkedin, Twitter, Facebook } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const OnThisPage = ({ htmlContent, tags = [], sharePath }) => {
  const [headings, setHeadings] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [shareUrl, setShareUrl] = useState('');

  // Extract h2, h3, h4 headings from provided HTML and build a tree
  useEffect(() => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent || '';
    const nodes = Array.from(tempDiv.querySelectorAll('h2, h3, h4'));

    const sections = [];
    let currentH2 = null;
    let currentH3 = null;

    nodes.forEach((node) => {
      const level = Number(node.tagName.replace('H', ''));
      const item = { id: node.id, text: node.textContent, level, children: [] };
      if (level === 2) {
        sections.push(item);
        currentH2 = item;
        currentH3 = null;
      } else if (level === 3) {
        if (currentH2) {
          currentH2.children.push(item);
          currentH3 = item;
        } else {
          sections.push(item);
        }
      } else if (level === 4) {
        if (currentH3) {
          currentH3.children.push(item);
        } else if (currentH2) {
          currentH2.children.push(item);
        } else {
          sections.push(item);
        }
      }
    });

    setHeadings(sections);
  }, [htmlContent]);

  // Determine share URL using BaseUrl and provided path or current path
  useEffect(() => {
    try {
      let path = sharePath;
      if (!path && typeof window !== 'undefined') {
        const current = window.location?.pathname || '';
        // If on admin preview, convert to public blog path
        path = current.startsWith('/admin/blogs/') ? current.replace('/admin/blogs/', '/blogs/') : current;
      }
      setShareUrl(`${BaseUrl}${path || ''}`);
    } catch (e) {
      setShareUrl(BaseUrl);
    }
  }, [sharePath]);

  // Track active section using IntersectionObserver (h2-h4)
  useEffect(() => {
    if (!headings?.length) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) setActiveId(entry.target.id);
        });
      },
      { root: null, rootMargin: '0px 0px -70% 0px', threshold: 0.1 }
    );

    const ids = [];
    headings.forEach((h2) => {
      if (h2?.id) ids.push(h2.id);
      (h2?.children || []).forEach((h3) => {
        if (h3?.id) ids.push(h3.id);
        (h3?.children || []).forEach((h4) => { if (h4?.id) ids.push(h4.id); });
      });
    });

    const elements = ids.map((id) => document.getElementById(id)).filter(Boolean);
    elements.forEach((el) => observer.observe(el));
    return () => elements.forEach((el) => observer.unobserve(el));
  }, [headings]);

  const encodedUrl = encodeURIComponent(shareUrl || BaseUrl);
  const shareLinks = {
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
    } catch (e) {
      console.error('Failed to copy', e);
    }
  };

  const defaultOpenIds = headings.filter(h => (h.children || []).length > 0).map(h => h.id);

  return (
    <aside
      className="hidden lg:block sticky right-8 top-24 min-w-64 bg-background dark:bg-accent shadow-lg border border-gray-200 dark:border-gray-700 rounded-xl p-6 overflow-y-auto max-h-[80vh]"
    >
      <h2 className="text-2xl font-semibold mb-4">Table of Contents</h2>
      <Accordion type="multiple" defaultValue={defaultOpenIds} className="space-y-2">
        {headings.map((h2) => {
          const hasChildren = (h2.children && h2.children.length > 0);
          const secActive = activeId === h2.id || (h2.children || []).some(h3 => h3.id === activeId || (h3.children || []).some(h4 => h4.id === activeId));
          if (!hasChildren) {
            const isActive = activeId === h2.id;
            return (
              <div key={h2.id} className={(isActive ? 'bg-primary/5' : '') + 'rounded-lg px-3 py-2 hover:bg-primary/5'}>
                <Link href={`#${h2.id}`} className="block text-left text-sm font-medium hover:underline">
                  {h2.text}
                </Link>
              </div>
            );
          }
          return (
            <AccordionItem key={h2.id} value={h2.id} className="border-none">
              <AccordionTrigger className={(secActive ? 'bg-primary/5' : '') + 'rounded-lg px-3 py-2 hover:bg-muted'}>
                <Link href={`#${h2.id}`} className="flex-1 text-left">
                  {h2.text}
                </Link>
              </AccordionTrigger>
              <AccordionContent>
                <ul className="ml-2 space-y-1">
                  {h2.children.map((h3) => (
                    <li key={h3.id}>
                      <Link href={`#${h3.id}`} className={(activeId === h3.id ? 'text-foreground ' : 'text-foreground/70 ') + 'block rounded-md px-3 py-1 hover:bg-primary/5'}>
                        {h3.text}
                      </Link>
                      {h3.children && h3.children.length > 0 && (
                        <ul className="ml-3 mt-1 space-y-1">
                          {h3.children.map((h4) => (
                            <li key={h4.id}>
                              <Link href={`#${h4.id}`} className={(activeId === h4.id ? 'text-foreground ' : 'text-foreground/70 ') + 'block rounded-md px-3 py-1 hover:bg-primary/5'}>
                                {h4.text}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      <div className="mt-6">
        <h3 className="text-base font-semibold mb-3">Share</h3>
        <div className="flex items-center gap-3">
          <button onClick={copyToClipboard} className="w-9 h-9 rounded-full border flex items-center justify-center hover:bg-muted">
            <LinkIcon className="w-4 h-4" />
          </button>
          <a href={shareLinks.linkedin} target="_blank" rel="noopener noreferrer" className="w-9 h-9 rounded-full border flex items-center justify-center hover:bg-muted">
            <Linkedin className="w-4 h-4" />
          </a>
          <a href={shareLinks.twitter} target="_blank" rel="noopener noreferrer" className="w-9 h-9 rounded-full border flex items-center justify-center hover:bg-muted">
            <Twitter className="w-4 h-4" />
          </a>
          <a href={shareLinks.facebook} target="_blank" rel="noopener noreferrer" className="w-9 h-9 rounded-full border flex items-center justify-center hover:bg-muted">
            <Facebook className="w-4 h-4" />
          </a>
        </div>
      </div>

      {Array.isArray(tags) && tags.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {tags.map((tag, idx) => (
            <span key={idx} className="text-xs px-3 py-1 rounded-md bg-muted text-foreground/80">
              {tag}
            </span>
          ))}
        </div>
      )}
    </aside>
  );
};

export default OnThisPage;
