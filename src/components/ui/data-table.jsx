import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Search } from "lucide-react";
import { <PERSON><PERSON> } from "./button";
import { Input } from "./input";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "./dropdown-menu";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./table";

export function DataTable({
  data,
  columns,
  loading,
  displayButtons = true,
  displayFilters = true,
  additionalFilters = ''
}) {
  const [sorting, setSorting] = React.useState([]);
  const [columnFilters, setColumnFilters] = React.useState([]);
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Get filterable columns (exclude action columns and columns with enableHiding: false)
  const filterableColumns = React.useMemo(() => {
    return columns
      .filter(column =>
        column.id !== "actions" &&
        column.filterable &&
        column.enableHiding !== false &&
        typeof column.accessorKey === "string"
      )
      .map(column => ({
        id: column.id || column.accessorKey,
        label: column.header?.toString() || column.accessorKey
      }));
  }, [columns]);

  // Initialize selectedColumn with the first filterable column's id
  const [selectedColumn, setSelectedColumn] = React.useState(undefined);

  React.useEffect(() => {
    if (filterableColumns.length > 0 && !selectedColumn) {
      setSelectedColumn(filterableColumns[0].id);
    }
  }, [filterableColumns, selectedColumn]);


  const table = useReactTable({
    data: data || [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  if (loading) {
    return <div>Loading...</div>;
  }

  // Don't render the filter section if there are no filterable columns
  if (filterableColumns.length === 0) {
    return (
      <div className="w-full">
        {/* Rest of the table without filter section */}
        {/* ... */}
      </div>
    );
  }
  console.log(selectedColumn);

  return (
    <div className="w-full">
      <div className="flex flex-row-reverse items-center gap-4 py-4">
        {(selectedColumn && displayFilters) && (
          <>
            {additionalFilters}
            <Select
              name="columns"
              value={selectedColumn}
              onChange={(value) => setSelectedColumn(value)}
              className="w-full text-sm"
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select column to filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Column</SelectLabel>
                  {filterableColumns.map((col) => (
                    <SelectItem key={col.id} value={col.id}>
                      {col.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>

            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Filter by ${filterableColumns.find(col => col.id === selectedColumn)?.label.toLowerCase() || 'column'}...`}
                value={selectedColumn ? (table?.getColumn(selectedColumn)?.getFilterValue() ?? "") : ""}
                onChange={(event) => {
                  table?.getColumn(selectedColumn)?.setFilterValue(event?.target?.value);
                }}
                className="pl-8 w-full bg-[var(--accent)]"
              />
            </div>
          </>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger
            title={'Columns'}
            icon={<ChevronDown className="w-4 h-4 opacity-50 ml-10" />}
          />
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {
        displayButtons && (
          <div className="flex items-center justify-end space-x-2 py-4">
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="text-xs w-[70px]"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                className="text-xs w-[70px]"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        )
      }
    </div>
  );
};
