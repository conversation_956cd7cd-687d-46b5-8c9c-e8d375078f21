"use client";

import { useState } from "react";
import { Check, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

export function DynamicMultiDatalist({
  items = [],
  existing = [],
  getLabel = (item) => item?.label || "",
  onChange,
  placeholder = "Search...",
}) {
  const [query, setQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState(existing);
  const [open, setOpen] = useState(false);

  const handleChange = (val) => {
    setQuery(val);
    setOpen(val.trim() !== "");
  };

  const toggleSelect = (item) => {
    const exists = selectedItems.some((i) => i.id === item.id);
    let newSelection;

    if (exists) {
      newSelection = selectedItems.filter((i) => i.id !== item.id);
    } else {
      newSelection = [...selectedItems, item];
    }

    setSelectedItems(newSelection);
    onChange?.(newSelection);
    setQuery("");
    setOpen(false);
  };

  const removeSelected = (itemId) => {
    const newSelection = selectedItems.filter((i) => i.id !== itemId);
    setSelectedItems(newSelection);
    onChange?.(newSelection);
  };

  const filtered = items.filter((item) =>
    getLabel(item).toLowerCase().includes(query.toLowerCase())
  );

  return (
    <div className="flex flex-col gap-2 relative">

      {/* Selected tags */}
      {selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedItems.map((item) => (
            <span
              key={item.id}
              className="flex items-center gap-1 px-2 py-1 bg-muted rounded-md text-sm"
            >
              {getLabel(item)}
              <X
                className="h-4 w-4 cursor-pointer"
                onClick={() => removeSelected(item.id)}
              />
            </span>
          ))}
        </div>
      )}

      {/* Input */}
      <Input
        type="text"
        value={query}
        onChange={(e) => handleChange(e.target.value)}
        placeholder={placeholder}
        autoComplete="off"
      />

      {/* Dropdown */}
      {open && (
        <div className="absolute top-[calc(100%+4px)] z-10 w-full">
          <Command>
            <CommandInput
              value={query}
              onValueChange={handleChange}
              placeholder={placeholder}
            />
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {filtered.map((item) => {
                  const isSelected = selectedItems.some(
                    (i) => i.id === item.id
                  );
                  return (
                    <CommandItem
                      key={item.id}
                      onSelect={() => toggleSelect(item)}
                      className="flex justify-between"
                    >
                      <span>{getLabel(item)}</span>
                      {isSelected && <Check className="h-4 w-4 text-primary" />}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
};
