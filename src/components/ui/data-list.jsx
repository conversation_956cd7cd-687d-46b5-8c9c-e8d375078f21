"use client";

import { useState } from "react";
import { Check } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

export function DynamicDatalist({
  label,
  items = [],
  getLabel = (item) => item?.label || "",
  onSelect,
  placeholder = "Search...",
}) {
  const [value, setValue] = useState("");
  const [selectedId, setSelectedId] = useState(null);
  const [open, setOpen] = useState(false);

  const handleChange = (val) => {
    setValue(val);
    setSelectedId(null);
    setOpen(val.trim() !== "");
  };

  const handleSelect = (item) => {
    setValue(getLabel(item));
    setSelectedId(item.id);
    onSelect?.(item);
    setOpen(false);
  };

  const filtered = items.filter((item) =>
    getLabel(item).toLowerCase().includes(value.toLowerCase())
  );

  return (
    <div className="flex flex-col gap-2 relative">
      {label && <Label htmlFor="datalist-input">{label}</Label>}

      {/* Main input */}
      <Input
        id="datalist-input"
        type="text"
        value={value}
        onChange={(e) => handleChange(e.target.value)}
        placeholder={placeholder}
        autoComplete="off"
      />

      {/* Dropdown */}
      {open && (
        <div className="absolute top-[7dvh] z-10 w-full">
          <Command>
            <CommandInput
              value={value}
              onValueChange={handleChange}
              placeholder={placeholder}
            />
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {filtered.map((item) => (
                  <CommandItem
                    key={item.id}
                    onSelect={() => handleSelect(item)}
                    className="flex justify-between"
                  >
                    <span>{getLabel(item)}</span>
                    {selectedId === item.id && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
};
