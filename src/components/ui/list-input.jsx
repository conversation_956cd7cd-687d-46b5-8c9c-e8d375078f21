import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus } from 'lucide-react';

export default function ListInput({
  items = [],
  placeholder = 'Add item',
  onChange,
  className = ''
}) {
  const [value, setValue] = useState('');
  const [list, setList] = useState(items || []);
  const [displayAll, setDisplayAll] = useState(false);

  useEffect(() => {
    setList(items || []);
  }, [items]);

  const addItem = () => {
    const v = value.trim();
    if (!v) return;
    const next = [...list, v];
    setList(next);
    onChange(next);
    setValue('');
  };

  const removeItem = (index) => {
    const next = list.filter((_, i) => i !== index);
    setList(next);
    onChange(next);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex gap-2">
        <Input value={value} onChange={(e) => setValue(e.target.value)} placeholder={placeholder} />
        <Button
          type="button"
          onClick={addItem}
          variant={'outline'}
        >
          Add <Plus size={18} />
        </Button>
      </div>
      {
        displayAll ? (
          <div className="flex flex-wrap gap-2">
            {list
              ?.map((item, idx) => (
                <span key={idx} className="inline-flex items-center gap-2 px-2 py-1 rounded-full bg-muted text-foreground text-xs">
                  {item}
                  <button type="button" onClick={() => removeItem(idx)} className="text-muted-foreground hover:text-destructive">×</button>
                </span>
              ))}
            <span
              onClick={() => setDisplayAll(false)}
              className="inline-flex items-center gap-2 px-2 py-1 rounded-full  border border-primary bg-primary/10 text-primary text-xs cursor-pointer"
            >
              Hide
            </span>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2">
            {list
              ?.slice(0, 4)
              ?.map((item, idx) => (
                <span key={idx} className="inline-flex items-center gap-2 px-2 py-1 rounded-full bg-muted text-foreground text-xs">
                  {item}
                  <button type="button" onClick={() => removeItem(idx)} className="text-muted-foreground hover:text-destructive">×</button>
                </span>
              ))}
            {(list?.length > 4 && !displayAll) && (
              <span
                onClick={() => setDisplayAll(true)}
                className="inline-flex items-center gap-2 px-2 py-1 rounded-full border border-primary bg-primary/10 text-primary text-xs cursor-pointer"
              >
                Show All
              </span>
            )}
          </div>
        )
      }
    </div>
  );
};
